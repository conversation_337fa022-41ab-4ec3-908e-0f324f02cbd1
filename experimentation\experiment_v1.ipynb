{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "DPkxBfTVp-Ag"}, "outputs": [], "source": ["!pip install google_adk --quiet\n", "!pip install cssutils --quiet\n", "!pip install openai faiss-cpu --quiet\n", "!pip install tavily-python --quiet\n", "!pip install qdrant_client --quiet\n", "!pip install loguru --quiet"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "YrXTxvwepXyr"}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'google.adk'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mgo<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01madk\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01magents\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m Agent\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mgoogle\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01madk\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtools\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m FunctionTool\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01madk\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mrunners\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Runner\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'google.adk'"]}], "source": ["from google.adk.agents import Agent\n", "from google.adk.tools import FunctionTool\n", "from google.adk.runners import Runner\n", "from google.adk.sessions import InMemorySessionService\n", "from google.adk.tools import ToolContext\n", "from google.genai import types\n", "from google.adk.planners import BuiltInPlanner\n", "from tavily import TavilyClient\n", "from IPython.display import HTML\n", "import logging\n", "import os\n", "import re\n", "from experimentation.prompt_template_daniel_duy_v2 import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WBXA9cSqvAIv"}, "outputs": [], "source": ["import os\n", "load_dotenv()\n", "\n", "os.environ['GOOGLE_API_KEY'] = userdata.get('GOOGLE_API_KEY')\n", "os.environ['TAVILY_API_KEY'] = userdata.get('TAVILY_API_KEY')\n", "os.environ[\"QDRANT_API_KEY\"] = userdata.get(\"QDRANT_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Uv9UwghcvEij"}, "outputs": [], "source": ["from google import genai\n", "\n", "client = genai.Client()\n", "\n", "MODEL_ID = \"gemini-2.5-pro\""]}, {"cell_type": "markdown", "metadata": {"id": "q9i0HSMx1Mqb"}, "source": ["# Helpers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "so8uj5bm1Otr"}, "outputs": [], "source": ["def find_text_in_between_tags(text, start_tag, end_tag, inclusive=False):\n", "    # Handle None or empty text\n", "    if text is None:\n", "        print(\"❌ Warning: LLM returned None response\")\n", "        return \"\"\n", "    if not isinstance(text, str):\n", "        print(f\"❌ Warning: LLM returned non-string response: {type(text)}\")\n", "        return \"\"\n", "\n", "    result = []\n", "    start_pos = text.find(start_tag)\n", "    end_pos = text.find(end_tag)\n", "    while start_pos > -1 and end_pos > -1:\n", "\n", "        if inclusive:\n", "            text_between_tags = text[start_pos : end_pos + len(end_tag)].strip()\n", "        else:\n", "            text_between_tags = text[start_pos + len(start_tag):end_pos].strip()\n", "\n", "        result.append(text_between_tags)\n", "        start_pos = text.find(start_tag, end_pos + len(end_tag))\n", "        end_pos = text.find(end_tag, start_pos)\n", "    res1 = \"\".join(result).strip()\n", "    res2 = re.sub(r\"\\[IMAGE\\].*?\\[/IMAGE\\]\", '', res1).strip()\n", "    return res2 if len(result) > 0 else \"\""]}, {"cell_type": "markdown", "metadata": {"id": "aZm0ep5PwKD5"}, "source": ["# Base Model Calling function -Replicating current pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "scL5N8q9wOEv"}, "outputs": [], "source": ["# Base API call structure\n", "def call(query : str):\n", "        response = client.models.generate_content(\n", "            model=MODEL_ID,\n", "            contents=query,\n", "            config=genai.types.GenerateContentConfig(\n", "                temperature=0.2,\n", "                thinking_config=types.ThinkingConfig(thinking_budget=128),\n", "                tools=[image_tavily, search_similar_logos_qdrant]\n", "                )\n", "        )\n", "\n", "        output = {\"text\" : response.text,\n", "                  \"full_response\": response}\n", "        return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VBMejvKa0PPO"}, "outputs": [], "source": ["def generate_title_slide(query : str) -> str:\n", "        logger.info(\"Generating title slide...\")\n", "        html_code = \\\n", "            find_text_in_between_tags(call(query=query)['text'],\n", "                                      start_tag=\"<!DOCTYPE html>\",\n", "                                      end_tag=\"</html>\",\n", "                                      inclusive=True)\n", "        return html_code"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "V5_HPzq_455T"}, "outputs": [], "source": ["from loguru import logger\n", "\n", "def generate_general_slide(query : str, slide_content : str, existing_slide_content : list) -> str:\n", "    \"\"\"\n", "    Generates a single slide's HTML content based on a query and the context of existing slides.\n", "    \"\"\"\n", "    logger.info(\"Generating a general slide...\")\n", "\n", "    try:\n", "        # CORRECTED: The 'thinking' parameter is removed from the call\n", "        llm_response = call(query=query)\n", "        if llm_response is None or 'text' not in llm_response:\n", "            logger.error(\"LLM returned invalid response\")\n", "            raise Exception(\"LLM returned invalid response\")\n", "\n", "        llm_text = llm_response['text']\n", "        if llm_text is None:\n", "            logger.error(\"LLM returned None text\")\n", "            raise Exception(\"LLM returned None text\")\n", "\n", "        html_code = find_text_in_between_tags(llm_text,\n", "                                              start_tag=\"<!DOCTYPE html>\",\n", "                                              end_tag=\"</html>\",\n", "                                              inclusive=True)\n", "\n", "        if not html_code:\n", "            logger.error(\"No HTML content found in LLM response\")\n", "            html_code = \"\"\"<!DOCTYPE html>\n", "            <html>\n", "            <head><style>body { font-family: Arial; padding: 20px; }</style></head>\n", "            <body><h1>Error: No HTML Content</h1><p>The model did not return valid HTML. Please try again.</p></body>\n", "            </html>\"\"\"\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Error in LLM call: {e}\")\n", "        html_code = f\"\"\"<!DOCTYPE html>\n", "        <html>\n", "        <head><style>body {{ font-family: Arial; padding: 20px; color: red; }}</style></head>\n", "        <body><h1>Error Generating Slide</h1><p>Error: {str(e)}</p></body>\n", "        </html>\"\"\"\n", "\n", "    return html_code"]}, {"cell_type": "markdown", "metadata": {"id": "svadGZhnvUvZ"}, "source": ["# Tavily Image Search tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EokSdKE_vX5A"}, "outputs": [], "source": ["def image_tavily(search_term: str) -> dict:\n", "    \"\"\"\n", "    tool_name: image_tavily\n", "\n", "    Use only when you need to search for complex illustration/structures or Background Image.\n", "\n", "    You use image_tavily to search for complex illustration/structures given a search term image_tavily will return image urls with the image\n", "    with th corresponding description.\n", "\n", "    Args:\n", "        search_term: The keyword or phrase for the image search.\n", "\n", "    Returns:\n", "        A list of dictionaries, each representing an image result with its\n", "        URL and description. Returns an empty list if no images are found.\n", "    \"\"\"\n", "    logging.log(logging.INFO, f\"🔍 Tavily Searching for: '{search_term}'\")\n", "    print(f\"🔍Tavily Searching for: '{search_term}'\")\n", "    client = TavilyClient()\n", "    response = client.search(\n", "        query=search_term,\n", "        include_images=True,\n", "        include_image_descriptions=True\n", "    )\n", "    logging.log(logging.INFO, f\"Found: {len(response)} images\")\n", "    return response.get(\"images\", [])"]}, {"cell_type": "markdown", "metadata": {"id": "dR_42sgMveKN"}, "source": ["# Qdrant Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 521, "referenced_widgets": ["ea93e3517a7d40b4a1f3403c93e93fc5", "15db0f4d98c84ecb97d9aec8c7a3994b", "fd57034d9088484cb35a240fbdd0042b", "1be26a1c0f3f467b9296851019a40ab2", "b00a35d1a5d14bdda6f33c2388bc5250", "ce0e3ba63ef8490b9c4109bc79c67090", "39f8900b4ad3492a83edc2548bab47f8", "5fb3b6332cef401f82371daedac5475a", "61a1fc9f72d949c78b015dbef850098e", "46f7428d02cc450cb50cf98732fa2a53", "0b35f73723f84f64ae268b3b2f4c4c99", "c1456ed60c6a4938a1ef7592b083aea6", "4d0aacf7d9f744ecaf347d8a563b70ae", "7dad2166383346fc845f0195d02a5d06", "fe9fc50dc8dd4043a9a291f060312e53", "965f407882b741b390c6ab609192d4a1", "f45079644bf04efd915cde5de832aabf", "29587a7bc49f4acb9104581ba29ba3dc", "fbe7037ec97041a1a41f12641ae2a8cb", "edb8b1f7532244ffbc0e1f37be758d0c", "ccc92a745d38442ca36c61aa50d7dc6c", "6beb00dcfeb54c979bac27b541549216", "5d1f00e3148b4d0f8831fd3a0c16a822", "a91006eccdf84c0db1486483f996d2e0", "90d1f22276a242a3ab8c3d70ed87cb3c", "a4cc184287cb405bbde40cdb94166d6d", "c5026f63cb314124a4754e1ed0a31247", "4236a96d7dae49bf832abcca74fe8ee4", "4d223b4565e74e8292c974f7944b7562", "a1ddab702b4b4a0a8e6223331efa6f05", "6d869ef355a7450482bf4967e5fab7f0", "580cafd4252c4a379ffd8f542c7526bf", "7df1bb4e163942529e8e3084ee194e42", "3ca38029c662487f977ea8a9047ec228", "b30e77e0a32441fe96b82f5ee148cbff", "4527da52da254bc2aa6982807d50df4a", "ccf8a74bd7e44c68a645988ff1ce9230", "e8f82f55b47f4237a63f5d89f9c51ede", "6a47ad6948b5480da382af420d5326cf", "d9df8b9c6d5c446c8258c7c696313057", "f49b11881e1f4ec68026cd821d1c6ae1", "10d380f565e54e48ba26c6f841b3ec95", "1a6bd4800ec44e18831d57ee3ce8d7da", "3d51df6215274aab8704a15e6d19a2a3", "4775cbc48327419bad6676d2b656ea91", "b763de7fae4b43158ecc2f5789ecca05", "076b659fc5004335baeb07eff2eb76ea", "48d0578e192940599b700d5e8c4d531c", "274a168cef8849749fdf1599c07408ed", "3f26d85cfb59436c856d8470097a7478", "293440b5510a4268989e3fcea144ff66", "6372c2b8852449018630b88d06e391fe", "dd8982d45bdd461a8305c339c0b01f42", "d7a889100e584cbf8990ade7352a3c88", "def9673616974430b8f76e657a37101b", "73d700ae686c4c4295b258caed55b8d6", "6db0d668f95947d4a4a0f32dca081dfb", "9c81288844a84920af8c44b340f5249a", "4511101fb1944733a693a10c5e637cda", "fcdf4dcf21c649d4871de652a943e815", "5bd29b7b3f8541468c18f3f559f68d3b", "3a2e46e317f24a99b6ee677ed463427e", "db537784b6214292a33080f2a10aacab", "abd2d6781f4a41e0844814b4cabbbcf6", "57e960f82aa644439236d16ded565216", "62e2d49108c04cda9cc665d0ddc50f20", "b959242e06a445749cf02f234a36112c", "7b26d480a6ee4621b94950fa4a72021e", "a2692dcd074d44b2b572473919966dee", "cfb56f8b8dfb4235be6f0707e40c6e63", "5e251b0e5cae4496bd149a63385130ed", "7f36183a5c7248748b36b3b427375f2e", "6fad0a4aa3664078bdae799ddd14a752", "0a8de638e0024f51bbc8eb4b941dd5ea", "1e65157e24764abb9a0512749302bdf5", "3566ab3b9fca473aba362ec4a6af86e9", "2e090cad26374b1e9a916bb88a8f65f6", "eff0d5bdf471457685a77b1161206e20", "db38be6bf05344218d45bdb8c2e0bb1c", "4197aa055e2c41b3ae7feb969aed77ec", "cdc5c17124d24b44a36d60c6edd30ac6", "e79a8152715442678d55dd8018187fdd", "bd07daf10228412b85e112c54b339628", "500e9587ada34f6888079343f722b030", "6e7b86435e984372afd1bb12baffd1c6", "377c8ccc3b3d4b7cb6e88635f1b5fc61", "82e3d218904741f88d8205ee7ba9e8e7", "c0b2c7fc0c7f4a8f838d9f352375487b", "e86dddafaaa248188a0f48139d65b37b", "b917085185c64f939537b9af35372433", "58b9614c5b524e49bf83d4eebb1b3b1d", "e8c3ba8b4dfa4f9487587be677261adb", "420d5fd4236d4ddfbb48d42bf5365ef1", "aa27ec9709ba41bda729d663d6059fa7", "d2b764726b8640e18daff532208b61e4", "9e6911bc9d424c36a6010ba25ebff9fb", "fc517e4aa0d942d287d868c7278faf8e", "1a609dce99b24395afcaddc5c4309140", "a2bec94803bf4e2f82fd07ad543fef9f", "a41640def05a4ec0a446c496445aa19c", "6b0f798ec708443996e7928dfe3beaf0", "f7b9f802fde146da8e3399332e9c67fc", "8b273022f6d74895aa2828121f109ac3", "4c21f26065fa4b62a5b625e51f8ab31b", "6b12db388b664380b0b2061b6f96001b", "c4dcffaefa8744858d9ad6a9e60bba93", "9ea5db05d8de43b8a22aade10d530c91", "4ef240c9da6f45b68045783963b5ee0d", "ab50f2d3002e43329e1ef86c8f1ed595", "15bf646409de468ab2a26a67de26eeb6", "36dbbb8ffbb64ef8977433e45074d349", "c07bdf5d4fc3477a8ffe0ec7ef4904ff", "5983152bac5f4cfca6ab389a445ecd09", "7eae65b2a6ba4209aa62cc9454f67d9e", "3d1b67706bc5473b9cd58c1c622e95b6", "baffaa8fd0f44f478eeef941ae2e01fe", "638f0831256e4614b5b202a61371da19", "dfcf6c00a8e3434c8666a7e4b0924040", "c5852a97f8954cbab1bdb0d173a09299", "02251f43a40b42c2a571307f04b80537", "491c9d750db148d7a8a9a4bdf60c096d"]}, "id": "FgGYVaPKviGu", "outputId": "35912408-3de7-4a84-df50-910af875f596"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading SentenceTransformer model...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ea93e3517a7d40b4a1f3403c93e93fc5", "version_major": 2, "version_minor": 0}, "text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1456ed60c6a4938a1ef7592b083aea6", "version_major": 2, "version_minor": 0}, "text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d1f00e3148b4d0f8831fd3a0c16a822", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ca38029c662487f977ea8a9047ec228", "version_major": 2, "version_minor": 0}, "text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4775cbc48327419bad6676d2b656ea91", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "73d700ae686c4c4295b258caed55b8d6", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b959242e06a445749cf02f234a36112c", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eff0d5bdf471457685a77b1161206e20", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.txt: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e86dddafaaa248188a0f48139d65b37b", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a41640def05a4ec0a446c496445aa19c", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "36dbbb8ffbb64ef8977433e45074d349", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Model loaded\n"]}], "source": ["import os\n", "from sentence_transformers import SentenceTransformer\n", "from loguru import logger\n", "from qdrant_client import QdrantClient\n", "\n", "from google.colab import userdata\n", "QDRANT_API_KEY = userdata.get('QDRANT_API_KEY')\n", "\n", "qdrant_client = QdrantClient(\n", "    url=\"https://defda113-4528-42a2-a623-cda5649a49e5.europe-west3-0.gcp.cloud.qdrant.io\",\n", "    api_key=QDRANT_API_KEY,\n", "    timeout=60\n", ")\n", "\n", "collection_name_icons = \"icon_embeddings\"\n", "collection_name_logos = \"logo_embeddings\"\n", "\n", "# === Load SentenceTransformer model ===\n", "print(\"Loading SentenceTransformer model...\")\n", "model = SentenceTransformer(\"all-MiniLM-L6-v2\")\n", "print(\"✅ Model loaded\")\n", "\n", "\n", "# === Search logos using Qdrant ===\n", "def search_similar_logos_qdrant(query_text: str):\n", "    \"\"\"\n", "    Search for any types of Vectors/SVG in the collection of 55000 SVGs.\n", "\n", "    Use for Logos/Icons and Stickers. Unlimited usage permitted.\n", "\n", "    The returned result is a list of dictionaries, where each dictionary contains:\n", "        - \"name\": The name of the logo or icon as stored in Qdrant payload\n", "        - \"url\" : The URL pointing to the logo or icon resource\n", "\n", "    Args:\n", "        query_text (str): The search text used to find Vectors/SVG\n", "    Returns:\n", "        list[dict]: A list of dictionaries containing only the \"name\" and \"url\"\n", "                    for each of the top matching logos/icons, sorted by similarity.\n", "\n", "    Example:\n", "        search_similar_logos_qdrant(\"apple logo\")\n", "        [\n", "            {\"name\": \"Apple Logo\", \"url\": \"https://example.com/apple.png\"},\n", "            {\"name\": \"<PERSON>\", \"url\": \"https://example.com/mac.png\"},\n", "            {\"name\": \"iPhone Symbol\", \"url\": \"https://example.com/iphone.png\"}\n", "        ]\n", "    \"\"\"\n", "    top_k = 5\n", "\n", "    logger.info(f\"Searching for logos/icons similar to: '{query_text}'\")\n", "    query_embedding = model.encode([query_text])[0]\n", "\n", "    search_result = qdrant_client.search(\n", "        collection_name=\"icon_multicolors_embeddings\",\n", "        query_vector=query_embedding,\n", "        limit=top_k\n", "    )\n", "    results = [\n", "        {\"name\": hit.payload[\"name\"], \"url\": hit.payload[\"url\"]}\n", "        for hit in search_result\n", "    ][:top_k]\n", "\n", "    logger.info(f\"Found {len(results)} results for query '{query_text}'\")\n", "\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9hIUHmhYta0F", "outputId": "a76e1bc8-9d3e-4dec-c663-45a2556c234a"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:13:13.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'Apple'\u001b[0m\n", "/tmp/ipython-input-1836134849.py:54: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_result = qdrant_client.search(\n", "\u001b[32m2025-08-04 06:13:13.732\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'Apple'\u001b[0m\n"]}, {"data": {"text/plain": ["[{'name': '<PERSON>',\n", "  'url': {'light': 'https://svgl.app/library/apple.svg',\n", "   'dark': 'https://svgl.app/library/apple_dark.svg'}},\n", " {'name': '<PERSON>', 'url': 'https://www.svgrepo.com/show/506709/apple.svg'},\n", " {'name': '<PERSON>', 'url': 'https://www.svgrepo.com/show/499881/apple.svg'},\n", " {'name': '<PERSON>', 'url': 'https://www.svgrepo.com/show/494385/apple.svg'},\n", " {'name': '<PERSON>', 'url': 'https://www.svgrepo.com/show/500025/apple.svg'}]"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["search_similar_logos_qdrant(\"Apple\")"]}, {"cell_type": "markdown", "metadata": {"id": "ing9XZX9vnfC"}, "source": ["# -----------------------User Query Set----------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VJ-XbG_Ep0dc"}, "outputs": [], "source": ["# define a query\n", "query1 = \"Make a presentation about building a system which takes an \" \\\n", "\"internal company database of contract documents, \" \\\n", "\"and based on a user query, \" \\\n", "\"helps find the most relevant contract document in 6 slides\"\n", "\n", "query2 = \"Make me a presentation about migrating a legacy app written in COBOL to AWS. Suggest me a good approach to ensure the functionality and the mission critical nature of the app in 6 slides\"\n", "\n", "query3 = \"Make me a presentation about a market research into last year Renewable Enegrgy. in 6 slides\"\n", "\n", "query = \"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\""]}, {"cell_type": "markdown", "metadata": {"id": "iwuZhMRqqji8"}, "source": ["### Planner.py Brainstorm Prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "TlBz_Z1nqgRT", "outputId": "0b472e57-7e0b-48cd-e5b2-24660889b7d3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "\n", "Before starting work on the request, you need to brainstorm.\n", "From a technical perspective, how could something like this be done? \n", "Please use the following pointers to guide your thought process:\n", "- What is the most cutting-edge way to do this?\n", "- How can this be done using cloud services?\n", "\n"]}], "source": ["# Planner prompt for brainstorming ideas\n", "brainstorm_query = plannerpy_brainstorm_prompt_v2.format(query=query)\n", "print(brainstorm_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "ED5n13mov5DN", "outputId": "28d1adc1-1a8c-472a-b8c3-f8ba45a1e5c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Of course. Here is a brainstorm on how to approach this task from a technical perspective, focusing on cutting-edge and cloud-based solutions.\n", "\n", "### Brainstorming: A Technical Approach\n", "\n", "The request is to create a 6-slide presentation on last year's EV market research. From a technical standpoint, this can be broken down into two main phases:\n", "\n", "1.  **Data Aggregation and Analysis:** Gathering and making sense of the vast amount of information about the EV market.\n", "2.  **Content Synthesis and Presentation Generation:** Turning the analyzed data into a compelling, human-readable presentation.\n", "\n", "---\n", "\n", "### 1. What is the most cutting-edge way to do this?\n", "\n", "The most cutting-edge approach involves creating an **autonomous agent-based system**. This system would be powered by a Large Language Model (LLM) that can reason, plan, and execute tasks by calling various specialized tools (APIs).\n", "\n", "Here’s a conceptual workflow for such a system:\n", "\n", "**Phase 1: Data Aggregation & Analysis (The \"Researcher\" Agent)**\n", "\n", "1.  **Deconstruct the Goal:** The primary LLM (the \"Orchestrator\") receives the prompt: \"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\". It breaks this down into sub-tasks.\n", "2.  **Dynamic Search Strategy:** The Orchestrator tasks a \"Researcher\" agent to find data. Instead of a simple web search, the agent would:\n", "    *   **Identify Key Topics:** Determine the essential components of EV market research (e.g., market size, key players, sales data, growth trends, consumer demographics, charging infrastructure, future outlook).\n", "    *   **Query Multiple Sources:** Use advanced search tools to query financial news APIs (like Bloomberg, Reuters), market research firm reports (e.g., G<PERSON>ner, Forrester), government energy and transportation sites, and academic databases. It would look for datasets, charts, and expert analysis from the specified period (\"last year\").\n", "    *   **Data Extraction & Structuring:** The agent wouldn't just scrape text. It would use a multimodal model to extract data from tables, charts, and graphs found in PDFs and web pages, converting unstructured information into a structured format like JSON.\n", "\n", "**Phase 2: Content Synthesis & Presentation Generation (The \"Content\" and \"Designer\" Agents)**\n", "\n", "1.  **Synthesize a Narrative:** The Orchestrator passes the structured data to a \"Content Strategist\" agent. This agent's role is to:\n", "    *   **Identify the Core Story:** Analyze the data to find the key narrative. For example: \"Last year was defined by intense price competition and the rapid growth of Chinese brands.\"\n", "    *   **Outline the 6 Slides:** Create a logical flow for the presentation.\n", "        *   Slide 1: Title - \"The EV Market in [Last Year]: A Year of Acceleration\"\n", "        *   Slide 2: Executive Summary - Key takeaways.\n", "        *   Slide 3: Market Growth & Sales - Hard numbers and charts.\n", "        *   Slide 4: Competitive Landscape - Tesla vs. BYD vs. Legacy Auto.\n", "        *   Slide 5: Key Trends - Battery tech, charging, software.\n", "        *   Slide 6: Future Outlook - Predictions and challenges.\n", "2.  **Generate Slide Content:** The agent then writes the text for each slide—bullet points, speaker notes, and data callouts.\n", "3.  **Automated Asset Generation:** A \"Designer\" agent would then take over to visually build the presentation:\n", "    *   **Text-to-Image/Chart:** It would generate relevant charts and graphs directly from the structured data. For abstract concepts (like \"Future Outlook\"), it could use a diffusion model (like DALL-E 3 or Midjourney) to create compelling visuals.\n", "    *   **Iconography and Logos:** It would use a vector search tool to find and insert appropriate corporate logos (e.g., Tesla, Ford, BYD) and icons (e.g., charging plug, battery icon).\n", "    *   **Layout and Assembly:** Finally, the agent would use an API (like Microsoft Graph for PowerPoint or Google Slides API) to programmatically assemble the presentation, inserting the text and images into a pre-defined, professional template.\n", "\n", "The final output would be a fully-formed `.pptx` or Google Slides file, delivered to the user.\n", "\n", "---\n", "\n", "### 2. How can this be done using cloud services?\n", "\n", "This entire cutting-edge workflow can be built and deployed using a combination of cloud services, primarily from providers like **AWS, Google Cloud (GCP), and Microsoft Azure**.\n", "\n", "Here’s a sample architecture using a mix of cloud services:\n", "\n", "**1. Orchestration & Compute:**\n", "*   **AWS Step Functions or Google Cloud Workflows:** These services are perfect for orchestrating the multi-step agentic workflow described above. You can define the logic (e.g., \"Run Researcher, then Content Strategist, then Designer\") and manage the flow of data between steps.\n", "*   **Serverless Functions (AWS Lambda, Google Cloud Functions):** Each \"agent\" or \"tool call\" can be implemented as a serverless function. This is cost-effective and scalable, as you only pay for the compute time you use. For instance, `call_web_search_api` would be one Lambda function, and `generate_slide_image` would be another.\n", "\n", "**2. LLMs and AI Services:**\n", "*   **Managed LLM Services:**\n", "    *   **Amazon Bedrock (AWS):** Provides API access to a variety of foundation models (<PERSON>throp<PERSON>'s <PERSON>, <PERSON><PERSON>, etc.), allowing you to choose the best model for each sub-task (e.g., Claude 3 Opus for reasoning, Sonnet for speed).\n", "    *   **Google Vertex AI:** Offers access to Google's Gemini family of models (including the highly capable Gemini 1.5 Pro) and a platform for building and managing AI applications.\n", "    *   **Azure OpenAI Service:** Provides enterprise-grade, secure access to OpenAI's models like GPT-4.\n", "*   **Specialized AI Services:**\n", "    *   **Data Extraction:** **Amazon Textract** or **Google Document AI** can be used to parse PDFs and extract data from tables and forms.\n", "    *   **Vector Search:** **Amazon Kendra** or **Google Vertex AI Search** can perform advanced \"Retrieval Augmented Generation\" (RAG) over a private corpus of market research documents. For vector-specific searches like logos, a managed vector database like **Qdrant Cloud**, **Pinecone**, or **Vertex AI Vector Search** would be used.\n", "\n", "**3. Data Storage and APIs:**\n", "*   **Data Lake:** Raw data gathered from the web (HTML, PDFs, images) would be stored in an object storage service like **Amazon S3** or **Google Cloud Storage**.\n", "*   **Structured Data:** The extracted, structured JSON data would be stored in a NoSQL database like **Amazon DynamoDB** or **Google Firestore** for quick access by the content generation agents.\n", "*   **Presentation Assembly:** The final step would use the **Microsoft Graph API** or **Google Workspace API** to create the PowerPoint or Google Slides document, storing the final output back in S3 or Cloud Storage for user download.\n", "\n", "By leveraging these cloud services, the entire process becomes a scalable, resilient, and automated pipeline that can go from a simple user request to a finished, data-rich presentation in minutes.\n"]}], "source": ["# Basic API Call\n", "brainstorm=call(query=brainstorm_query)\n", "print(brainstorm['text'])"]}, {"cell_type": "markdown", "metadata": {"id": "qsFQswhgqrNI"}, "source": ["### Planner.py Outline Prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "mDnopOsVqqlK", "outputId": "bd084e68-dfa8-49a5-a2cb-ba5b65c73c7f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "After consulting with a senior software engineer, he has provided you the following approach to build such a system:\n", "\"{'text': 'Of course. Here is a brainstorm on how to approach this task from a technical perspective, focusing on cutting-edge and cloud-based solutions.\\n\\n### Brainstorming: A Technical Approach\\n\\nThe request is to create a 6-slide presentation on last year\\'s EV market research. From a technical standpoint, this can be broken down into two main phases:\\n\\n1.  **Data Aggregation and Analysis:** Gathering and making sense of the vast amount of information about the EV market.\\n2.  **Content Synthesis and Presentation Generation:** Turning the analyzed data into a compelling, human-readable presentation.\\n\\n---\\n\\n### 1. What is the most cutting-edge way to do this?\\n\\nThe most cutting-edge approach involves creating an **autonomous agent-based system**. This system would be powered by a Large Language Model (LLM) that can reason, plan, and execute tasks by calling various specialized tools (APIs).\\n\\nHere’s a conceptual workflow for such a system:\\n\\n**Phase 1: Data Aggregation & Analysis (The \"Researcher\" Agent)**\\n\\n1.  **Deconstruct the Goal:** The primary LLM (the \"Orchestrator\") receives the prompt: \"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\". It breaks this down into sub-tasks.\\n2.  **Dynamic Search Strategy:** The Orchestrator tasks a \"Researcher\" agent to find data. Instead of a simple web search, the agent would:\\n    *   **Identify Key Topics:** Determine the essential components of EV market research (e.g., market size, key players, sales data, growth trends, consumer demographics, charging infrastructure, future outlook).\\n    *   **Query Multiple Sources:** Use advanced search tools to query financial news APIs (like Bloomberg, Reuters), market research firm reports (e.g., Gartner, Forrester), government energy and transportation sites, and academic databases. It would look for datasets, charts, and expert analysis from the specified period (\"last year\").\\n    *   **Data Extraction & Structuring:** The agent wouldn\\'t just scrape text. It would use a multimodal model to extract data from tables, charts, and graphs found in PDFs and web pages, converting unstructured information into a structured format like JSON.\\n\\n**Phase 2: Content Synthesis & Presentation Generation (The \"Content\" and \"Designer\" Agents)**\\n\\n1.  **Synthesize a Narrative:** The Orchestrator passes the structured data to a \"Content Strategist\" agent. This agent\\'s role is to:\\n    *   **Identify the Core Story:** Analyze the data to find the key narrative. For example: \"Last year was defined by intense price competition and the rapid growth of Chinese brands.\"\\n    *   **Outline the 6 Slides:** Create a logical flow for the presentation.\\n        *   Slide 1: Title - \"The EV Market in [Last Year]: A Year of Acceleration\"\\n        *   Slide 2: Executive Summary - Key takeaways.\\n        *   Slide 3: Market Growth & Sales - Hard numbers and charts.\\n        *   Slide 4: Competitive Landscape - Tesla vs. BYD vs. Legacy Auto.\\n        *   Slide 5: Key Trends - Battery tech, charging, software.\\n        *   Slide 6: Future Outlook - Predictions and challenges.\\n2.  **Generate Slide Content:** The agent then writes the text for each slide—bullet points, speaker notes, and data callouts.\\n3.  **Automated Asset Generation:** A \"Designer\" agent would then take over to visually build the presentation:\\n    *   **Text-to-Image/Chart:** It would generate relevant charts and graphs directly from the structured data. For abstract concepts (like \"Future Outlook\"), it could use a diffusion model (like DALL-E 3 or Midjourney) to create compelling visuals.\\n    *   **Iconography and Logos:** It would use a vector search tool to find and insert appropriate corporate logos (e.g., Tesla, Ford, BYD) and icons (e.g., charging plug, battery icon).\\n    *   **Layout and Assembly:** Finally, the agent would use an API (like Microsoft Graph for PowerPoint or Google Slides API) to programmatically assemble the presentation, inserting the text and images into a pre-defined, professional template.\\n\\nThe final output would be a fully-formed `.pptx` or Google Slides file, delivered to the user.\\n\\n---\\n\\n### 2. How can this be done using cloud services?\\n\\nThis entire cutting-edge workflow can be built and deployed using a combination of cloud services, primarily from providers like **AWS, Google Cloud (GCP), and Microsoft Azure**.\\n\\nHere’s a sample architecture using a mix of cloud services:\\n\\n**1. Orchestration & Compute:**\\n*   **AWS Step Functions or Google Cloud Workflows:** These services are perfect for orchestrating the multi-step agentic workflow described above. You can define the logic (e.g., \"Run Researcher, then Content Strategist, then Designer\") and manage the flow of data between steps.\\n*   **Serverless Functions (AWS Lambda, Google Cloud Functions):** Each \"agent\" or \"tool call\" can be implemented as a serverless function. This is cost-effective and scalable, as you only pay for the compute time you use. For instance, `call_web_search_api` would be one Lambda function, and `generate_slide_image` would be another.\\n\\n**2. LLMs and AI Services:**\\n*   **Managed LLM Services:**\\n    *   **Amazon Bedrock (AWS):** Provides API access to a variety of foundation models (Anthropic\\'s Claude, Cohere, etc.), allowing you to choose the best model for each sub-task (e.g., Claude 3 Opus for reasoning, Sonnet for speed).\\n    *   **Google Vertex AI:** Offers access to Google\\'s Gemini family of models (including the highly capable Gemini 1.5 Pro) and a platform for building and managing AI applications.\\n    *   **Azure OpenAI Service:** Provides enterprise-grade, secure access to OpenAI\\'s models like GPT-4.\\n*   **Specialized AI Services:**\\n    *   **Data Extraction:** **Amazon Textract** or **Google Document AI** can be used to parse PDFs and extract data from tables and forms.\\n    *   **Vector Search:** **Amazon Kendra** or **Google Vertex AI Search** can perform advanced \"Retrieval Augmented Generation\" (RAG) over a private corpus of market research documents. For vector-specific searches like logos, a managed vector database like **Qdrant Cloud**, **Pinecone**, or **Vertex AI Vector Search** would be used.\\n\\n**3. Data Storage and APIs:**\\n*   **Data Lake:** Raw data gathered from the web (HTML, PDFs, images) would be stored in an object storage service like **Amazon S3** or **Google Cloud Storage**.\\n*   **Structured Data:** The extracted, structured JSON data would be stored in a NoSQL database like **Amazon DynamoDB** or **Google Firestore** for quick access by the content generation agents.\\n*   **Presentation Assembly:** The final step would use the **Microsoft Graph API** or **Google Workspace API** to create the PowerPoint or Google Slides document, storing the final output back in S3 or Cloud Storage for user download.\\n\\nBy leveraging these cloud services, the entire process becomes a scalable, resilient, and automated pipeline that can go from a simple user request to a finished, data-rich presentation in minutes.', 'full_response': GenerateContentResponse(\n", "  automatic_function_calling_history=[],\n", "  candidates=[\n", "    Candidate(\n", "      content=Content(\n", "        parts=[\n", "          Part(\n", "            text=\"\"\"Of course. Here is a brainstorm on how to approach this task from a technical perspective, focusing on cutting-edge and cloud-based solutions.\n", "\n", "### Brainstorming: A Technical Approach\n", "\n", "The request is to create a 6-slide presentation on last year's EV market research. From a technical standpoint, this can be broken down into two main phases:\n", "\n", "1.  **Data Aggregation and Analysis:** Gathering and making sense of the vast amount of information about the EV market.\n", "2.  **Content Synthesis and Presentation Generation:** Turning the analyzed data into a compelling, human-readable presentation.\n", "\n", "---\n", "\n", "### 1. What is the most cutting-edge way to do this?\n", "\n", "The most cutting-edge approach involves creating an **autonomous agent-based system**. This system would be powered by a Large Language Model (LLM) that can reason, plan, and execute tasks by calling various specialized tools (APIs).\n", "\n", "Here’s a conceptual workflow for such a system:\n", "\n", "**Phase 1: Data Aggregation & Analysis (The \"Researcher\" Agent)**\n", "\n", "1.  **Deconstruct the Goal:** The primary LLM (the \"Orchestrator\") receives the prompt: \"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\". It breaks this down into sub-tasks.\n", "2.  **Dynamic Search Strategy:** The Orchestrator tasks a \"Researcher\" agent to find data. Instead of a simple web search, the agent would:\n", "    *   **Identify Key Topics:** Determine the essential components of EV market research (e.g., market size, key players, sales data, growth trends, consumer demographics, charging infrastructure, future outlook).\n", "    *   **Query Multiple Sources:** Use advanced search tools to query financial news APIs (like Bloomberg, Reuters), market research firm reports (e.g., G<PERSON>ner, Forrester), government energy and transportation sites, and academic databases. It would look for datasets, charts, and expert analysis from the specified period (\"last year\").\n", "    *   **Data Extraction & Structuring:** The agent wouldn't just scrape text. It would use a multimodal model to extract data from tables, charts, and graphs found in PDFs and web pages, converting unstructured information into a structured format like JSON.\n", "\n", "**Phase 2: Content Synthesis & Presentation Generation (The \"Content\" and \"Designer\" Agents)**\n", "\n", "1.  **Synthesize a Narrative:** The Orchestrator passes the structured data to a \"Content Strategist\" agent. This agent's role is to:\n", "    *   **Identify the Core Story:** Analyze the data to find the key narrative. For example: \"Last year was defined by intense price competition and the rapid growth of Chinese brands.\"\n", "    *   **Outline the 6 Slides:** Create a logical flow for the presentation.\n", "        *   Slide 1: Title - \"The EV Market in [Last Year]: A Year of Acceleration\"\n", "        *   Slide 2: Executive Summary - Key takeaways.\n", "        *   Slide 3: Market Growth & Sales - Hard numbers and charts.\n", "        *   Slide 4: Competitive Landscape - Tesla vs. BYD vs. Legacy Auto.\n", "        *   Slide 5: Key Trends - Battery tech, charging, software.\n", "        *   Slide 6: Future Outlook - Predictions and challenges.\n", "2.  **Generate Slide Content:** The agent then writes the text for each slide—bullet points, speaker notes, and data callouts.\n", "3.  **Automated Asset Generation:** A \"Designer\" agent would then take over to visually build the presentation:\n", "    *   **Text-to-Image/Chart:** It would generate relevant charts and graphs directly from the structured data. For abstract concepts (like \"Future Outlook\"), it could use a diffusion model (like DALL-E 3 or Midjourney) to create compelling visuals.\n", "    *   **Iconography and Logos:** It would use a vector search tool to find and insert appropriate corporate logos (e.g., Tesla, Ford, BYD) and icons (e.g., charging plug, battery icon).\n", "    *   **Layout and Assembly:** Finally, the agent would use an API (like Microsoft Graph for PowerPoint or Google Slides API) to programmatically assemble the presentation, inserting the text and images into a pre-defined, professional template.\n", "\n", "The final output would be a fully-formed `.pptx` or Google Slides file, delivered to the user.\n", "\n", "---\n", "\n", "### 2. How can this be done using cloud services?\n", "\n", "This entire cutting-edge workflow can be built and deployed using a combination of cloud services, primarily from providers like **AWS, Google Cloud (GCP), and Microsoft Azure**.\n", "\n", "Here’s a sample architecture using a mix of cloud services:\n", "\n", "**1. Orchestration & Compute:**\n", "*   **AWS Step Functions or Google Cloud Workflows:** These services are perfect for orchestrating the multi-step agentic workflow described above. You can define the logic (e.g., \"Run Researcher, then Content Strategist, then Designer\") and manage the flow of data between steps.\n", "*   **Serverless Functions (AWS Lambda, Google Cloud Functions):** Each \"agent\" or \"tool call\" can be implemented as a serverless function. This is cost-effective and scalable, as you only pay for the compute time you use. For instance, `call_web_search_api` would be one Lambda function, and `generate_slide_image` would be another.\n", "\n", "**2. LLMs and AI Services:**\n", "*   **Managed LLM Services:**\n", "    *   **Amazon Bedrock (AWS):** Provides API access to a variety of foundation models (<PERSON>throp<PERSON>'s <PERSON>, <PERSON><PERSON>, etc.), allowing you to choose the best model for each sub-task (e.g., Claude 3 Opus for reasoning, Sonnet for speed).\n", "    *   **Google Vertex AI:** Offers access to Google's Gemini family of models (including the highly capable Gemini 1.5 Pro) and a platform for building and managing AI applications.\n", "    *   **Azure OpenAI Service:** Provides enterprise-grade, secure access to OpenAI's models like GPT-4.\n", "*   **Specialized AI Services:**\n", "    *   **Data Extraction:** **Amazon Textract** or **Google Document AI** can be used to parse PDFs and extract data from tables and forms.\n", "    *   **Vector Search:** **Amazon Kendra** or **Google Vertex AI Search** can perform advanced \"Retrieval Augmented Generation\" (RAG) over a private corpus of market research documents. For vector-specific searches like logos, a managed vector database like **Qdrant Cloud**, **Pinecone**, or **Vertex AI Vector Search** would be used.\n", "\n", "**3. Data Storage and APIs:**\n", "*   **Data Lake:** Raw data gathered from the web (HTML, PDFs, images) would be stored in an object storage service like **Amazon S3** or **Google Cloud Storage**.\n", "*   **Structured Data:** The extracted, structured JSON data would be stored in a NoSQL database like **Amazon DynamoDB** or **Google Firestore** for quick access by the content generation agents.\n", "*   **Presentation Assembly:** The final step would use the **Microsoft Graph API** or **Google Workspace API** to create the PowerPoint or Google Slides document, storing the final output back in S3 or Cloud Storage for user download.\n", "\n", "By leveraging these cloud services, the entire process becomes a scalable, resilient, and automated pipeline that can go from a simple user request to a finished, data-rich presentation in minutes.\"\"\"\n", "          ),\n", "        ],\n", "        role='model'\n", "      ),\n", "      finish_reason=<FinishReason.STOP: 'STOP'>,\n", "      index=0\n", "    ),\n", "  ],\n", "  model_version='gemini-2.5-pro',\n", "  sdk_http_response=HttpResponse(\n", "    headers=<dict len=10>\n", "  ),\n", "  usage_metadata=GenerateContentResponseUsageMetadata(\n", "    candidates_token_count=1593,\n", "    prompt_token_count=545,\n", "    prompt_tokens_details=[\n", "      ModalityTokenCount(\n", "        modality=<MediaModality.TEXT: 'TEXT'>,\n", "        token_count=545\n", "      ),\n", "    ],\n", "    thoughts_token_count=73,\n", "    total_token_count=2211\n", "  )\n", ")}\"\n", "\n", "Some of the sections you should include are:\n", "- Title slide\n", "- Executive summary slide\n", "- The background of the problem\n", "- Your proposed solution and why it will work / benefits of the solution\n", "- The infrastructure and tech stack\n", "- The required human resources\n", "- The timeline\n", "- The cost involved in this project\n", "- A proper conclusion slide\n", "\n", "Depending on the situation, be creative and add in any other sections that you think might add value.\n", "If this proposal is successful, you will get a big raise!\n", "\n"]}], "source": ["# Planner prompt for creating an outline\n", "outline_query = plannerpy_outline_prompt_v2.format(query=query, brainstorm_response=brainstorm)\n", "print(outline_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "JprFB5cVzHeM", "outputId": "ff2cd09b-995a-455a-ba4d-9f1fc5d891e5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is the proposal for building the automated presentation generation system.\n", "\n", "### **Slide 1: Title Slide**\n", "\n", "**(Image: A futuristic, abstract image of data streams flowing into a presentation slide)**\n", "\n", "---\n", "\n", "**Project Sentinel: Automated Insight-to-Presentation Engine**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n", "### **Slide 2: Executive Summary**\n", "\n", "---\n", "\n", "**The Opportunity:** The demand for timely, data-driven market analysis is accelerating. However, the process of manually gathering data, synthesizing insights, and creating compelling presentations is slow, resource-intensive, and prone to delays.\n", "\n", "**Our Proposed Solution:** We propose **Project Sentinel**, an autonomous, AI-powered system that transforms a simple request into a fully-realized, data-rich presentation. By leveraging a cutting-edge agent-based architecture, Project Sentinel will automate the entire workflow—from data aggregation and analysis to content generation and visual design.\n", "\n", "**Key Benefits:**\n", "\n", "*   **Speed:** Reduce presentation creation time from weeks to minutes.\n", "*   **Scalability:** Generate hundreds of bespoke reports simultaneously.\n", "*   **Data-Driven:** Ensure every presentation is built on the latest available market data.\n", "*   **Efficiency:** Free up high-value analysts and consultants from tedious manual tasks to focus on strategic interpretation and client engagement.\n", "\n", "---\n", "### **Slide 3: The Challenge: The Manual Research Bottleneck**\n", "\n", "---\n", "\n", "**(Image: A diagram showing a complex, tangled web of arrows connecting icons for 'Data', 'Analyst', 'Charts', and 'Slides', representing a chaotic manual process)**\n", "\n", "---\n", "\n", "In today's fast-paced market, the traditional approach to creating research presentations is no longer viable.\n", "\n", "*   **Time-Consuming:** Analysts spend an average of **40-60% of their time** gathering and formatting data, not analyzing it.\n", "*   **High Cost:** The manual effort translates directly into high operational costs and billable hours spent on low-value, repetitive tasks.\n", "*   **Inconsistent Output:** The quality and focus of presentations can vary significantly depending on the individual analyst, leading to an inconsistent brand experience.\n", "*   **Slow to React:** The lengthy creation cycle makes it difficult to respond quickly to new market developments or urgent client requests.\n", "\n", "This bottleneck stifles agility and limits the strategic impact of your expert teams.\n", "\n", "---\n", "### **Slide 4: Our Solution: Project Sentinel**\n", "\n", "---\n", "\n", "**(Image: A clean, streamlined diagram showing a single input prompt entering a \"black box\" labeled \"Project Sentinel\" and a finished PowerPoint/Google Slides file emerging)**\n", "\n", "---\n", "\n", "Project Sentinel is an **autonomous agent-based system** that intelligently orchestrates the entire presentation creation process.\n", "\n", "1.  **Deconstruction & Planning (The \"Orchestrator\" Agent):** The system receives a high-level goal (e.g., \"EV Market Research Presentation\"). It intelligently deconstructs this into a series of logical sub-tasks.\n", "\n", "2.  **Data Aggregation & Analysis (The \"Researcher\" Agent):** This agent dynamically queries a vast array of sources—financial news APIs, market research databases, government reports—and uses multimodal AI to extract and structure relevant data, charts, and text.\n", "\n", "3.  **Narrative Synthesis (The \"Content Strategist\" Agent):** The system analyzes the structured data to identify a core narrative and outlines a logical 6-slide story. It then generates concise, insightful text for each slide.\n", "\n", "4.  **Automated Design (The \"Designer\" Agent):** This agent programmatically assembles the final presentation. It generates charts from the data, searches for and inserts relevant logos and icons, and applies a professional, pre-defined brand template.\n", "\n", "The result is a polished, data-backed presentation, delivered in minutes.\n", "\n", "---\n", "### **Slide 5: Infrastructure & Tech Stack**\n", "\n", "---\n", "**(Image: A cloud architecture diagram showing the flow from Cloud Functions/Step Functions to AI/ML services like Vertex AI and finally to storage like S3/GCS)**\n", "---\n", "We will build Project Sentinel on a robust, scalable, and secure cloud foundation, leveraging best-in-class services for each component of the workflow.\n", "\n", "*   **Orchestration & Compute:**\n", "    *   **Google Cloud Workflows:** To manage the multi-step agentic process.\n", "    *   **Google Cloud Functions:** For cost-effective, serverless execution of individual tasks (e.g., API calls, data processing).\n", "\n", "*   **AI & Machine Learning:**\n", "    *   **Google Vertex AI:** To access the powerful Gemini family of models for reasoning, content generation, and analysis.\n", "    *   **Google Document AI:** For extracting structured data from unstructured sources like PDFs and reports.\n", "    *   **Qdrant Cloud:** A specialized vector database for ultra-fast and accurate searching of logos and icons.\n", "\n", "*   **Data & Storage:**\n", "    *   **Google Cloud Storage:** A secure and scalable data lake for storing raw and processed information.\n", "    *   **Google Firestore:** A NoSQL database for managing the structured data used by the content agents.\n", "\n", "*   **Presentation Assembly:**\n", "    *   **Google Workspace API / Microsoft Graph API:** To programmatically create and assemble the final Google Slides or PowerPoint file.\n", "\n", "---\n", "### **Slide 6: Required Team & Resources**\n", "\n", "---\n", "\n", "We recommend a lean, expert team to ensure the successful development and deployment of Project Sentinel.\n", "\n", "*   **Project Lead (1):** Oversees the project, manages timelines, and serves as the primary point of contact.\n", "*   **Lead AI/ML Engineer (1):** Designs the core agent architecture, fine-tunes LLM interactions, and integrates AI services.\n", "*   **Cloud Engineer (1):** Builds and manages the cloud infrastructure, ensures security, and sets up the data pipelines.\n", "*   **Software Engineer (1):** Develops the serverless functions, integrates APIs, and handles the application logic.\n", "\n", "This focused team of **four experts** will drive the project from conception to completion, ensuring a high-quality, robust, and scalable solution.\n", "\n", "---\n", "### **Slide 7: Project Timeline**\n", "\n", "---\n", "**(Image: A Gantt chart visualizing the 4 phases over a 12-week period)**\n", "---\n", "We propose an agile, 12-week timeline to deliver Project Sentinel.\n", "\n", "*   **Phase 1: Discovery & Architecture (Weeks 1-2)**\n", "    *   Finalize requirements and KPIs.\n", "    *   Design the detailed system architecture and select final toolsets.\n", "    *   Set up core cloud infrastructure and security protocols.\n", "\n", "*   **Phase 2: Core Development & Integration (Weeks 3-8)**\n", "    *   Develop the \"Researcher\" and \"Content Strategist\" agents.\n", "    *   Integrate data sources and AI services (Vertex AI, Qdrant).\n", "    *   Build the initial data processing and storage pipelines.\n", "\n", "*   **Phase 3: Design & Assembly (Weeks 9-10)**\n", "    *   <PERSON><PERSON>p the \"Designer\" agent.\n", "    *   Integrate with Google Slides/PowerPoint APIs.\n", "    *   Implement the presentation templating engine.\n", "\n", "*   **Phase 4: Testing & Deployment (Weeks 11-12)**\n", "    *   End-to-end system testing and quality assurance.\n", "    *   User Acceptance Testing (UAT) with a pilot group.\n", "    *   Final deployment and handover.\n", "\n", "---\n", "### **Slide 8: Cost & Investment**\n", "\n", "---\n", "\n", "This investment is designed to deliver a significant return by revolutionizing your content creation workflow.\n", "\n", "**One-Time Development Cost:**\n", "\n", "*   **Team (12 weeks):** $240,000\n", "    *   *(Based on 4 experts at a blended rate of $5,000/week/person)*\n", "*   **Software & Licensing:** $5,000\n", "    *   *(Includes subscriptions for specialized data APIs and services)*\n", "\n", "**Estimated Monthly Operational Cost (Post-Launch):**\n", "\n", "*   **Cloud Services:** ~$1,500 - $4,000 / month\n", "    *   *(Variable based on usage; includes compute, storage, and AI API calls)*\n", "*   **Maintenance & Support (Optional Retainer):** $10,000 / month\n", "    *   *(Includes ongoing monitoring, updates, and system optimizations)*\n", "\n", "**Total Initial Investment: $245,000**\n", "\n", "This investment unlocks massive efficiency gains, positioning your firm as a technology leader in data-driven analysis.\n", "\n", "---\n", "### **Slide 9: Conclusion & Next Steps**\n", "\n", "---\n", "\n", "Project Sentinel is more than an automation tool; it's a **strategic asset**.\n", "\n", "By automating the manual, time-consuming aspects of presentation creation, you will empower your team to focus on what they do best: delivering high-value strategic insights and strengthening client relationships. This system will provide a durable competitive advantage, enabling you to deliver superior analysis with unparalleled speed and scale.\n", "\n", "**Next Steps:**\n", "\n", "1.  **Formalize Agreement:** Finalize the project scope and statement of work.\n", "2.  **Kick-off Meeting:** Schedule a project kick-off to introduce the teams and begin the discovery phase.\n", "3.  **Begin Phase 1:** Commence the 12-week journey to transform your research capabilities.\n", "\n", "**Let's build the future of automated insights, together.**\n"]}], "source": ["outline = call(query=outline_query)\n", "print(outline['text'])"]}, {"cell_type": "markdown", "metadata": {"id": "i_Soc_lJrlMR"}, "source": ["### Planner.py Slide Prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "2e1rM-ZVq3i-", "outputId": "8a5f34d9-7018-4b31-c208-810c1e8709f3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "\n", "Based on the advice of the senior software engineer, you have planned out your presentation:\n", "\"Here is the proposal for building the automated presentation generation system.\n", "\n", "### **Slide 1: Title Slide**\n", "\n", "**(Image: A futuristic, abstract image of data streams flowing into a presentation slide)**\n", "\n", "---\n", "\n", "**Project Sentinel: Automated Insight-to-Presentation Engine**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n", "### **Slide 2: Executive Summary**\n", "\n", "---\n", "\n", "**The Opportunity:** The demand for timely, data-driven market analysis is accelerating. However, the process of manually gathering data, synthesizing insights, and creating compelling presentations is slow, resource-intensive, and prone to delays.\n", "\n", "**Our Proposed Solution:** We propose **Project Sentinel**, an autonomous, AI-powered system that transforms a simple request into a fully-realized, data-rich presentation. By leveraging a cutting-edge agent-based architecture, Project Sentinel will automate the entire workflow—from data aggregation and analysis to content generation and visual design.\n", "\n", "**Key Benefits:**\n", "\n", "*   **Speed:** Reduce presentation creation time from weeks to minutes.\n", "*   **Scalability:** Generate hundreds of bespoke reports simultaneously.\n", "*   **Data-Driven:** Ensure every presentation is built on the latest available market data.\n", "*   **Efficiency:** Free up high-value analysts and consultants from tedious manual tasks to focus on strategic interpretation and client engagement.\n", "\n", "---\n", "### **Slide 3: The Challenge: The Manual Research Bottleneck**\n", "\n", "---\n", "\n", "**(Image: A diagram showing a complex, tangled web of arrows connecting icons for 'Data', 'Analyst', 'Charts', and 'Slides', representing a chaotic manual process)**\n", "\n", "---\n", "\n", "In today's fast-paced market, the traditional approach to creating research presentations is no longer viable.\n", "\n", "*   **Time-Consuming:** Analysts spend an average of **40-60% of their time** gathering and formatting data, not analyzing it.\n", "*   **High Cost:** The manual effort translates directly into high operational costs and billable hours spent on low-value, repetitive tasks.\n", "*   **Inconsistent Output:** The quality and focus of presentations can vary significantly depending on the individual analyst, leading to an inconsistent brand experience.\n", "*   **Slow to React:** The lengthy creation cycle makes it difficult to respond quickly to new market developments or urgent client requests.\n", "\n", "This bottleneck stifles agility and limits the strategic impact of your expert teams.\n", "\n", "---\n", "### **Slide 4: Our Solution: Project Sentinel**\n", "\n", "---\n", "\n", "**(Image: A clean, streamlined diagram showing a single input prompt entering a \"black box\" labeled \"Project Sentinel\" and a finished PowerPoint/Google Slides file emerging)**\n", "\n", "---\n", "\n", "Project Sentinel is an **autonomous agent-based system** that intelligently orchestrates the entire presentation creation process.\n", "\n", "1.  **Deconstruction & Planning (The \"Orchestrator\" Agent):** The system receives a high-level goal (e.g., \"EV Market Research Presentation\"). It intelligently deconstructs this into a series of logical sub-tasks.\n", "\n", "2.  **Data Aggregation & Analysis (The \"Researcher\" Agent):** This agent dynamically queries a vast array of sources—financial news APIs, market research databases, government reports—and uses multimodal AI to extract and structure relevant data, charts, and text.\n", "\n", "3.  **Narrative Synthesis (The \"Content Strategist\" Agent):** The system analyzes the structured data to identify a core narrative and outlines a logical 6-slide story. It then generates concise, insightful text for each slide.\n", "\n", "4.  **Automated Design (The \"Designer\" Agent):** This agent programmatically assembles the final presentation. It generates charts from the data, searches for and inserts relevant logos and icons, and applies a professional, pre-defined brand template.\n", "\n", "The result is a polished, data-backed presentation, delivered in minutes.\n", "\n", "---\n", "### **Slide 5: Infrastructure & Tech Stack**\n", "\n", "---\n", "**(Image: A cloud architecture diagram showing the flow from Cloud Functions/Step Functions to AI/ML services like Vertex AI and finally to storage like S3/GCS)**\n", "---\n", "We will build Project Sentinel on a robust, scalable, and secure cloud foundation, leveraging best-in-class services for each component of the workflow.\n", "\n", "*   **Orchestration & Compute:**\n", "    *   **Google Cloud Workflows:** To manage the multi-step agentic process.\n", "    *   **Google Cloud Functions:** For cost-effective, serverless execution of individual tasks (e.g., API calls, data processing).\n", "\n", "*   **AI & Machine Learning:**\n", "    *   **Google Vertex AI:** To access the powerful Gemini family of models for reasoning, content generation, and analysis.\n", "    *   **Google Document AI:** For extracting structured data from unstructured sources like PDFs and reports.\n", "    *   **Qdrant Cloud:** A specialized vector database for ultra-fast and accurate searching of logos and icons.\n", "\n", "*   **Data & Storage:**\n", "    *   **Google Cloud Storage:** A secure and scalable data lake for storing raw and processed information.\n", "    *   **Google Firestore:** A NoSQL database for managing the structured data used by the content agents.\n", "\n", "*   **Presentation Assembly:**\n", "    *   **Google Workspace API / Microsoft Graph API:** To programmatically create and assemble the final Google Slides or PowerPoint file.\n", "\n", "---\n", "### **Slide 6: Required Team & Resources**\n", "\n", "---\n", "\n", "We recommend a lean, expert team to ensure the successful development and deployment of Project Sentinel.\n", "\n", "*   **Project Lead (1):** Oversees the project, manages timelines, and serves as the primary point of contact.\n", "*   **Lead AI/ML Engineer (1):** Designs the core agent architecture, fine-tunes LLM interactions, and integrates AI services.\n", "*   **Cloud Engineer (1):** Builds and manages the cloud infrastructure, ensures security, and sets up the data pipelines.\n", "*   **Software Engineer (1):** Develops the serverless functions, integrates APIs, and handles the application logic.\n", "\n", "This focused team of **four experts** will drive the project from conception to completion, ensuring a high-quality, robust, and scalable solution.\n", "\n", "---\n", "### **Slide 7: Project Timeline**\n", "\n", "---\n", "**(Image: A Gantt chart visualizing the 4 phases over a 12-week period)**\n", "---\n", "We propose an agile, 12-week timeline to deliver Project Sentinel.\n", "\n", "*   **Phase 1: Discovery & Architecture (Weeks 1-2)**\n", "    *   Finalize requirements and KPIs.\n", "    *   Design the detailed system architecture and select final toolsets.\n", "    *   Set up core cloud infrastructure and security protocols.\n", "\n", "*   **Phase 2: Core Development & Integration (Weeks 3-8)**\n", "    *   Develop the \"Researcher\" and \"Content Strategist\" agents.\n", "    *   Integrate data sources and AI services (Vertex AI, Qdrant).\n", "    *   Build the initial data processing and storage pipelines.\n", "\n", "*   **Phase 3: Design & Assembly (Weeks 9-10)**\n", "    *   <PERSON><PERSON>p the \"Designer\" agent.\n", "    *   Integrate with Google Slides/PowerPoint APIs.\n", "    *   Implement the presentation templating engine.\n", "\n", "*   **Phase 4: Testing & Deployment (Weeks 11-12)**\n", "    *   End-to-end system testing and quality assurance.\n", "    *   User Acceptance Testing (UAT) with a pilot group.\n", "    *   Final deployment and handover.\n", "\n", "---\n", "### **Slide 8: Cost & Investment**\n", "\n", "---\n", "\n", "This investment is designed to deliver a significant return by revolutionizing your content creation workflow.\n", "\n", "**One-Time Development Cost:**\n", "\n", "*   **Team (12 weeks):** $240,000\n", "    *   *(Based on 4 experts at a blended rate of $5,000/week/person)*\n", "*   **Software & Licensing:** $5,000\n", "    *   *(Includes subscriptions for specialized data APIs and services)*\n", "\n", "**Estimated Monthly Operational Cost (Post-Launch):**\n", "\n", "*   **Cloud Services:** ~$1,500 - $4,000 / month\n", "    *   *(Variable based on usage; includes compute, storage, and AI API calls)*\n", "*   **Maintenance & Support (Optional Retainer):** $10,000 / month\n", "    *   *(Includes ongoing monitoring, updates, and system optimizations)*\n", "\n", "**Total Initial Investment: $245,000**\n", "\n", "This investment unlocks massive efficiency gains, positioning your firm as a technology leader in data-driven analysis.\n", "\n", "---\n", "### **Slide 9: Conclusion & Next Steps**\n", "\n", "---\n", "\n", "Project Sentinel is more than an automation tool; it's a **strategic asset**.\n", "\n", "By automating the manual, time-consuming aspects of presentation creation, you will empower your team to focus on what they do best: delivering high-value strategic insights and strengthening client relationships. This system will provide a durable competitive advantage, enabling you to deliver superior analysis with unparalleled speed and scale.\n", "\n", "**Next Steps:**\n", "\n", "1.  **Formalize Agreement:** Finalize the project scope and statement of work.\n", "2.  **Kick-off Meeting:** Schedule a project kick-off to introduce the teams and begin the discovery phase.\n", "3.  **Begin Phase 1:** Commence the 12-week journey to transform your research capabilities.\n", "\n", "**Let's build the future of automated insights, together.**\"\n", "\n", "CRITICAL INSTRUCTIONS:\n", "1) You MUST follow the outline structure EXACTLY - do not add extra slides beyond what's in the outline\n", "2) If the original request specified a number of slides (e.g., \"1 slide\", \"3 slides\"), you MUST respect that limit\n", "3) Create content ONLY for the slides mentioned in the outline above\n", "4) Do NOT create additional slides even if you think more content would be helpful\n", "\n", "Following the plan you have created above, and referencing the technical advice of the senior software engineer,\n", "describe the content that will appear on EACH slide in detail.\n", "\n", "Pay extra attention to the following points:\n", "1) If a diagram or image should go on a slide (e.g. an infrastructure diagram, organization chart or a GANTT chart etc.),\n", "you must describe it with enough detail such that someone reading the description would be able to reproduce it perfectly.\n", "\n", "2) Make sure to include the content that should appear on the title slide.\n", "\n", "3) Make sure to include at least one slide with a table. Output this table in markdown.\n", "\n", "4) STICK TO THE OUTLINE - Do not exceed the number of slides specified in the outline or original request.\n", "\n", "5) Remember that these are powerpoint slides, do not overload them with too much content\n", "\n", "Think carefully about whether or not the needs of the client are being met with this proposal.\n", "If this proposal is successful, you will get a big raise!\n", "\n", "IMPORTANT: Make sure to separate the content of each slide with the following markers <Slide X START> and <Slide X END>, where X represents the slide number.\n", "REMEMBER: Only create slides that are mentioned in the outline above. Do not add extra slides.\n", "\n"]}], "source": ["# Planner prompt for defining slide content\n", "slide_content = plannerpy_slide_content_prompt_v2.format(query=query, outline_response=outline['text'], brainstorm_response=brainstorm[\"text\"])\n", "print(slide_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "G5EhaTcNz7qq", "outputId": "daf9ccdb-65dd-403a-887f-e641498938a7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### **Slide 1: Title Slide**\n", "\n", "**(Image: A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it.)**\n", "\n", "---\n", "\n", "**The Electric Vehicle Market: 2023 Year in Review**\n", "\n", "**A Data-Driven Analysis of Key Trends and Future Outlook**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n"]}], "source": ["slide_content_response = call(query=slide_content)\n", "print(find_text_in_between_tags(text=slide_content_response['text'], start_tag=\"<Slide 1 START>\", end_tag=\"<Slide 1 END>\", inclusive=False))"]}, {"cell_type": "markdown", "metadata": {"id": "8qArpwU0sI27"}, "source": ["### Generationpy_title_slide_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "TnvZ75Q2sICn", "outputId": "0df36b3e-32a8-474d-d3e2-8b9a2ff6366a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "\n", "You are trying to create a set of slides for a proposal.\n", "The first slide you want to create is the title slide.\n", "Generate the title slide in HTML.\n", "\n", "Take into consideration the following points:\n", "- Choose a style that is both visually appealing and functional; befitting of a proposal from a top-tier tech consulting company.\n", "- What colour and design would be appropriate, especially for the background?\n", "- What font type should you use?\n", "- What should the size of the page be, to accurately reflect powerpoint slides? The slides must be 720p\n", "- The title font should be around 3.0em, and the subtitle around 1.8em, otherwise it is too big.\n", "- Do not include a footer.\n", "- The HTML code MUST include the following configurations:\n", ".slide-container {\n", "        width: 1280px; /* 720p resolution width */\n", "        height: 720px;\n", "        ... The rest is not specified and to your discretion ...  \n", "        }\n", "\n", ".slide-content {\n", "        flex-grow: 1; /* Allows this section to take up available space */\n", "        overflow : hidden; \n", "        display: flex;\n", "        flex-direction: column;\n", "        justify-content: flex-start; /* Align content to the top */\n", "        align-items: flex-start; /* Align content to the left */\n", "        text-align: left; /* Default text alignment */\n", "        width: 100%; /* Ensure it takes full width of padding area */\n", "        padding-top: 0; /* Remove extra padding from title slide */\n", "        }\n", "\n", "This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.\n", "Do not output any other text other than the html itself.\n", "If your slides are visually appealing but also functional, you will be rewarded with a bonus.\n", "\n", "The information that should be included on this slide is as follows:\n", "### **Slide 1: Title Slide**\n", "\n", "**(Image: A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it.)**\n", "\n", "---\n", "\n", "**The Electric Vehicle Market: 2023 Year in Review**\n", "\n", "**A Data-Driven Analysis of Key Trends and Future Outlook**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n", "\n"]}], "source": ["title_slide_query = (generationpy_title_slide_prompt_v2.format(query=query, title_slide_content=(find_text_in_between_tags(text=slide_content_response['text'], start_tag=\"<Slide 1 START>\", end_tag=\"<Slide 1 END>\", inclusive=False))))\n", "print (title_slide_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YpfHOnl82-oC", "outputId": "24bda9eb-b239-4dc1-8d4b-b62aa9b9089d"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 05:58:25.641\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_title_slide\u001b[0m:\u001b[36m2\u001b[0m - \u001b[1mGenerating title slide...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔍Tavily Searching for: 'A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it'\n"]}], "source": ["title_slide_html = generate_title_slide(query=title_slide_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h5CuXiiLG_Mo", "outputId": "effb0445-e432-4f59-faf8-921f240d7d1e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "<title>EV Market Research</title>\n", "<style>\n", "    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');\n", "\n", "    body, html {\n", "        margin: 0;\n", "        padding: 0;\n", "        display: flex;\n", "        justify-content: center;\n", "        align-items: center;\n", "        height: 100vh;\n", "        background-color: #f0f0f0;\n", "        font-family: '<PERSON><PERSON>', sans-serif;\n", "    }\n", "\n", "    .slide-container {\n", "        width: 1280px; /* 720p resolution width */\n", "        height: 720px;\n", "        background-color: #1A1A1A; /* Dark background for a tech feel */\n", "        display: flex;\n", "        flex-direction: column;\n", "        justify-content: center;\n", "        align-items: center;\n", "        position: relative;\n", "        overflow: hidden;\n", "        box-shadow: 0 10px 20px rgba(0,0,0,0.2);\n", "    }\n", "\n", "    .background-image {\n", "        position: absolute;\n", "        top: 0;\n", "        left: 0;\n", "        width: 100%;\n", "        height: 100%;\n", "        background-image: url('https://img.freepik.com/premium-photo/electric-car-charging-sunset-with-wind-turbines-background_1203353-45441.jpg');\n", "        background-size: cover;\n", "        background-position: center;\n", "        filter: brightness(0.4); /* Darken the image to make text pop */\n", "        z-index: 1;\n", "    }\n", "\n", "    .slide-content {\n", "        position: relative;\n", "        z-index: 2;\n", "        color: #FFFFFF;\n", "        flex-grow: 1; /* Allows this section to take up available space */\n", "        overflow : hidden;\n", "        display: flex;\n", "        flex-direction: column;\n", "        justify-content: flex-start; /* Align content to the top */\n", "        align-items: flex-start; /* Align content to the left */\n", "        text-align: left; /* Default text alignment */\n", "        width: 100%; /* Ensure it takes full width of padding area */\n", "        padding: 60px 80px; /* Generous padding for a clean look */\n", "        box-sizing: border-box;\n", "    }\n", "\n", "    .slide-content h1 {\n", "        font-size: 3.0em;\n", "        font-weight: 700;\n", "        margin-top: 150px; /* Pushes the title down a bit */\n", "        margin-bottom: 20px;\n", "        line-height: 1.2;\n", "        max-width: 90%; /* Prevents text from touching the edge */\n", "    }\n", "\n", "    .slide-content p {\n", "        font-size: 1.8em;\n", "        font-weight: 300;\n", "        margin-top: 20px;\n", "        margin-bottom: 10px;\n", "        max-width: 90%;\n", "    }\n", "\n", "</style>\n", "</head>\n", "<body>\n", "\n", "    <div class=\"slide-container\">\n", "        <div class=\"background-image\"></div>\n", "        <div class=\"slide-content\">\n", "            <h1><b>Electric Vehicle Market Research: A 2023 Retrospective and Future Outlook</b></h1>\n", "            <p><b>Prepared for:</b> [Client Name]</p>\n", "            <p><b>Date:</b> October 26, 2023</p>\n", "            <p><b>Presented by:</b> [Your Name/Company Name], Tech Consultant</p>\n", "        </div>\n", "    </div>\n", "\n", "</body>\n", "</html>\n"]}], "source": ["print(title_slide_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 999}, "id": "G2zDOXqP3rdx", "outputId": "c56b94ae-7843-498b-c7c9-32ad55437723"}, "outputs": [{"data": {"text/html": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "<title>EV Market Research</title>\n", "<style>\n", "    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');\n", "\n", "    body, html {\n", "        margin: 0;\n", "        padding: 0;\n", "        display: flex;\n", "        justify-content: center;\n", "        align-items: center;\n", "        height: 100vh;\n", "        background-color: #f0f2f5;\n", "        font-family: '<PERSON><PERSON>', sans-serif;\n", "    }\n", "\n", "    .slide-container {\n", "        width: 1280px; /* 720p resolution width */\n", "        height: 720px;\n", "        background-color: #0d1b2a; /* Dark blue-gray for a professional, techy feel */\n", "        display: flex;\n", "        flex-direction: column;\n", "        justify-content: center;\n", "        align-items: center;\n", "        position: relative;\n", "        overflow: hidden;\n", "        box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);\n", "    }\n", "\n", "    .background-image {\n", "        position: absolute;\n", "        top: 0;\n", "        left: 0;\n", "        width: 100%;\n", "        height: 100%;\n", "        background-image: url('https://thumbs.dreamstime.com/b/futuristic-electric-car-charging-night-under-glowing-structure-sleek-circular-showcasing-innovative-sustainable-357867492.jpg');\n", "        background-size: cover;\n", "        background-position: center;\n", "        opacity: 0.25; /* Subtle background effect */\n", "        z-index: 1;\n", "    }\n", "\n", "    .slide-content {\n", "        position: relative;\n", "        z-index: 2;\n", "        flex-grow: 1; /* Allows this section to take up available space */\n", "        overflow : hidden;\n", "        display: flex;\n", "        flex-direction: column;\n", "        justify-content: center; /* Vertically center content */\n", "        align-items: flex-start; /* Align content to the left */\n", "        text-align: left;\n", "        width: 100%;\n", "        padding: 60px 80px; /* Generous padding for a clean look */\n", "        box-sizing: border-box;\n", "        color: #ffffff;\n", "    }\n", "\n", "    .title {\n", "        font-size: 3.0em; /* As requested */\n", "        font-weight: 700;\n", "        margin-bottom: 20px;\n", "        line-height: 1.2;\n", "        color: #ffffff;\n", "        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);\n", "    }\n", "\n", "    .subtitle {\n", "        font-size: 1.8em; /* As requested */\n", "        font-weight: 300;\n", "        margin-bottom: 40px;\n", "        color: #e0e0e0;\n", "        text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);\n", "    }\n", "\n", "    .details {\n", "        font-size: 1.1em;\n", "        font-weight: 400;\n", "        color: #c0c0c0;\n", "        line-height: 1.6;\n", "    }\n", "\n", "    .details strong {\n", "        font-weight: 700;\n", "        color: #ffffff;\n", "    }\n", "\n", "</style>\n", "</head>\n", "<body>\n", "\n", "<div class=\"slide-container\">\n", "    <div class=\"background-image\"></div>\n", "    <div class=\"slide-content\">\n", "        <h1 class=\"title\">The Electric Vehicle Market: 2023 Year in Review</h1>\n", "        <h2 class=\"subtitle\">A Data-Driven Analysis of Key Trends and Future Outlook</h2>\n", "        <div class=\"details\">\n", "            <p><strong>Prepared for:</strong> [Client Name]</p>\n", "            <p><strong>Prepared by:</strong> [Your Name/Consultancy]</p>\n", "            <p><strong>Date:</strong> October 26, 2023</p>\n", "        </div>\n", "    </div>\n", "</div>\n", "\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["# Rendering\n", "html_title_slide = HTML(title_slide_html)\n", "html_title_slide"]}, {"cell_type": "markdown", "metadata": {"id": "6RKVp7PYtK1t"}, "source": ["### Generationpy_agenda_slide_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "0D2gvaFTsja0", "outputId": "24c15c91-f6e4-4189-bf17-2a920186dff2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "\n", "You are trying to create a set of slides for a proposal using HTML and TailwindCSS.\n", "You have finished the title slide.\n", "\n", "Title slide HTML:\n", "```html\n", "None\n", "```\n", "\n", "Next, create the agenda slide in full HTML and Tailwind CSS.\n", "\n", "Slide content to include:\n", "None\n", "\n", "You need to pay EXTREME ATTENTION to the position of the elements inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS\n", "\n", "# ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS\n", "\n", "Constraints:\n", "- Conform to the design style of the existing slides for consistency\n", "- Eliminate the use of animations and transitions.\n", "- The css component describing the main content area must be called .slide-content\n", "- DO NOT include the presenter name, their title, the date and any company logos on this slide. This is to save space.\n", "- Titles should be aligned to the top left-hand side of the slide\n", "- The slides MUST be 720p, and ensure all slide content fits within a height of 720px.\n", "- Output only the final HTML for the new slide.\n", "\n", "\n", "Beyond all your mission is to make this BEAUTIFUL\n", "REMEMBER: You must output FULL HTML and Tailwind CSS, starting with <!DOCTYPE html> and ending with </html> and NOTHING ELSE\n", "\n"]}], "source": ["agenda_slide_query = generationpy_agenda_slide_prompt_v2.format(query=query, title_slide_html=None, agenda_slide_content=None)\n", "print (agenda_slide_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_iWl2Ndm4GWZ", "outputId": "6a65933a-ed54-4384-bb78-42c70b30705d"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:09:25.570\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_title_slide\u001b[0m:\u001b[36m2\u001b[0m - \u001b[1mGenerating title slide...\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.067\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'magnifying glass icon'\u001b[0m\n", "/tmp/ipython-input-1836134849.py:54: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_result = qdrant_client.search(\n", "\u001b[32m2025-08-04 06:09:30.391\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'magnifying glass icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.392\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'line chart icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.518\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'line chart icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.519\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'user icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.642\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'user icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.643\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'car icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.766\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'car icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.767\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'lightbulb icon'\u001b[0m\n", "\u001b[32m2025-08-04 06:09:30.891\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'lightbulb icon'\u001b[0m\n"]}], "source": ["agenda_slide_html = generate_title_slide(query=agenda_slide_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 801}, "id": "hX2txk9b4llI", "outputId": "642e3f61-249c-4651-a077-5aa10b710450"}, "outputs": [{"data": {"text/html": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name of=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>Agenda Slide</title>\n", "    <script src=\"https://cdn.tailwindcss.com\"></script>\n", "    <style>\n", "        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap');\n", "        body {\n", "            font-family: 'Inter', sans-serif;\n", "        }\n", "    </style>\n", "</head>\n", "<body class=\"bg-gray-100\">\n", "    <div class=\"w-[1280px] h-[720px] bg-white shadow-lg mx-auto my-8\">\n", "        <div class=\"slide-content w-full h-full flex flex-col justify-center p-16\">\n", "            <div class=\"w-full max-w-6xl mx-auto\">\n", "                <h1 class=\"text-5xl font-bold text-gray-800 mb-16\">Agenda</h1>\n", "                <div class=\"space-y-8\">\n", "                    <div class=\"flex items-center\">\n", "                        <div class=\"flex-shrink-0 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/513480/magnifying-glass.svg\" alt=\"Market Overview\" class=\"w-8 h-8 text-blue-600\">\n", "                        </div>\n", "                        <div class=\"ml-6\">\n", "                            <h2 class=\"text-3xl font-semibold text-gray-700\">Market Overview</h2>\n", "                            <p class=\"text-xl text-gray-500 mt-1\">A look at the global EV market size and growth in the past year.</p>\n", "                        </div>\n", "                    </div>\n", "                    <div class=\"flex items-center\">\n", "                        <div class=\"flex-shrink-0 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/444228/chart-line.svg\" alt=\"Key Trends\" class=\"w-8 h-8 text-green-600\">\n", "                        </div>\n", "                        <div class=\"ml-6\">\n", "                            <h2 class=\"text-3xl font-semibold text-gray-700\">Key Trends & Innovations</h2>\n", "                            <p class=\"text-xl text-gray-500 mt-1\">Exploring technological advancements and emerging market trends.</p>\n", "                        </div>\n", "                    </div>\n", "                    <div class=\"flex items-center\">\n", "                        <div class=\"flex-shrink-0 w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/475081/icon.svg\" alt=\"Consumer Demographics\" class=\"w-8 h-8 text-purple-600\">\n", "                        </div>\n", "                        <div class=\"ml-6\">\n", "                            <h2 class=\"text-3xl font-semibold text-gray-700\">Consumer Demographics</h2>\n", "                            <p class=\"text-xl text-gray-500 mt-1\">Understanding the profile of the modern EV buyer.</p>\n", "                        </div>\n", "                    </div>\n", "                    <div class=\"flex items-center\">\n", "                        <div class=\"flex-shrink-0 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/513282/car.svg\" alt=\"Competitive Landscape\" class=\"w-8 h-8 text-red-600\">\n", "                        </div>\n", "                        <div class=\"ml-6\">\n", "                            <h2 class=\"text-3xl font-semibold text-gray-700\">Competitive Landscape</h2>\n", "                            <p class=\"text-xl text-gray-500 mt-1\">Analysis of major players and their market share.</p>\n", "                        </div>\n", "                    </div>\n", "                    <div class=\"flex items-center\">\n", "                        <div class=\"flex-shrink-0 w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/474685/lightbulb.svg\" alt=\"Future Outlook\" class=\"w-8 h-8 text-yellow-600\">\n", "                        </div>\n", "                        <div class=\"ml-6\">\n", "                            <h2 class=\"text-3xl font-semibold text-gray-700\">Future Outlook & Recommendations</h2>\n", "                            <p class=\"text-xl text-gray-500 mt-1\">Forecasting future growth and strategic recommendations.</p>\n", "                        </div>\n", "                    </div>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["# Rendering\n", "html_agenda_slide = HTML(agenda_slide_html)\n", "html_agenda_slide"]}, {"cell_type": "markdown", "metadata": {"id": "40dMiTOXtv0S"}, "source": ["### Generationpy_slide_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FSDy1Tu4tcad", "outputId": "a947712e-362f-4115-db5b-42bc18bd30ae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "You are a tech consultant, and you have been given the following request:\n", "\"Make me a presentation about a market research into last year Electric Vehicle. in 6 slides\"\n", "\n", "You are trying to create a set of slides for a proposal using HTML and TailwindCSS, and have so far created some slides already: \n", "\n", "<Slide 1 START>\n", "### **Slide 1: Title Slide**\n", "\n", "**(Image: A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it.)**\n", "\n", "---\n", "\n", "**The Electric Vehicle Market: 2023 Year in Review**\n", "\n", "**A Data-Driven Analysis of Key Trends and Future Outlook**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n", "<Slide 1 END>\n", "<Slide 2 START>\n", "### **Slide 2: Executive Summary**\n", "\n", "---\n", "\n", "The global Electric Vehicle (EV) market experienced unprecedented growth in 2023, driven by government incentives, technological advancements, and increasing consumer demand for sustainable transportation. This presentation provides a concise overview of the key market dynamics, sales leaders, and emerging trends from the past year.\n", "\n", "**Key Highlights from 2023:**\n", "\n", "*   **Record-Breaking Sales:** Global EV sales surged by over 30%, with key markets in Asia and Europe leading the charge.\n", "*   **Intensified Competition:** While established players maintained significant market share, new entrants and legacy automakers accelerated their EV offerings, increasing competition.\n", "*   **Infrastructure Expansion:** Significant investments were made in expanding the global charging infrastructure, though it remains a critical factor for mass adoption.\n", "*   **Future Outlook:** The market is poised for continued growth, with a strong focus on battery innovation, software-defined vehicles, and affordability.\n", "\n", "---\n", "<Slide 2 END>\n", "<Slide 3 START>\n", "### **Slide 3: 2023 Global Sales Snapshot**\n", "\n", "---\n", "\n", "**(Image: A world map with three key regions highlighted: North America, Europe, and East Asia. Each region has a large, bold percentage number next to it indicating its share of the global EV market (e.g., East Asia: 58%, Europe: 25%, North America: 12%). A small EV icon is placed within each highlighted region.)**\n", "\n", "---\n", "\n", "Global EV sales demonstrated robust growth, though adoption rates varied significantly by region.\n", "\n", "| Region | 2023 Sales (Units) | Year-over-Year Growth | Key Market Drivers |\n", "| :--- | :--- | :--- | :--- |\n", "| **Asia-Pacific** | ~7.8 Million | +30% | Strong government subsidies, domestic manufacturing |\n", "| **Europe** | ~3.2 Million | +28% | Strict emission regulations, wide model availability |\n", "| **North America** | ~1.4 Million | +45% | Federal tax credits, new model launches (trucks/SUVs) |\n", "| **Rest of World** | ~0.6 Million | +25% | Nascent market growth, initial infrastructure build-out |\n", "\n", "*Note: Sales figures are approximate based on preliminary full-year data.*\n", "\n", "---\n", "<Slide 3 END>\n", "<Slide 4 START>\n", "### **Slide 4: Competitive Landscape: Market Leaders**\n", "\n", "---\n", "\n", "**(Image: A simple bar chart titled \"Top 5 Global EV Manufacturers by Market Share (2023)\". The Y-axis is \"Market Share (%)\" and the X-axis lists the top 5 company names. Each bar is a different color and has a corresponding logo next to the name on the X-axis. For example: Tesla (Bar at ~18%), BYD (Bar at ~16%), Volkswagen Group (Bar at ~9%), etc.)**\n", "\n", "---\n", "\n", "The competitive landscape in 2023 was a tale of two titans leading the pack, with legacy automakers and new challengers vying for position.\n", "\n", "1.  **Tesla:** Maintained its global leadership position, driven by the popularity of the Model Y and strategic price adjustments.\n", "2.  **BYD:** Showcased explosive growth, particularly in the Asian market, challenging for the top spot with a diverse portfolio of affordable EVs and plug-in hybrids.\n", "3.  **Volkswagen Group:** Solidified its position as a top contender with a broad range of EVs across its various brands (VW, Audi, Skoda).\n", "4.  **Geely-Volvo:** Leveraged its multi-brand strategy (Volvo, Polestar, Zeekr) to capture significant market share, especially in the premium segment.\n", "5.  **Hyundai/Kia:** Continued to gain market share with critically acclaimed and popular models like the IONIQ 5 and EV6.\n", "\n", "---\n", "<Slide 4 END>\n", "<Slide 5 START>\n", "### **Slide 5: Key Trends That Shaped 2023**\n", "\n", "---\n", "\n", "**(Image: A diagram with a central circle labeled \"2023 EV Trends\". Four arrows point outwards from this circle to four smaller icons, each with a label.)**\n", "*   **Icon 1: A battery with a \"down arrow\" on price.** Label: **Battery Cost Reduction**\n", "*   **Icon 2: A pickup truck silhouette.** Label: **Segment Diversification**\n", "*   **Icon 3: A charging plug.** Label: **Charging Infrastructure Race**\n", "*   **Icon 4: A computer chip.** Label: **Software & Autonomy**\n", "\n", "---\n", "\n", "Beyond sales figures, four critical trends defined the EV market's trajectory last year.\n", "\n", "*   **Battery Technology & Cost:** The average cost per kWh continued to fall, making EVs more affordable. Focus intensified on new chemistries like LFP (Lithium Iron Phosphate) for standard-range models.\n", "*   **Diversification of Segments:** The market expanded beyond sedans. 2023 saw a significant rise in the availability and sales of electric SUVs and pickup trucks, appealing to a broader consumer base.\n", "*   **The Charging Infrastructure Race:** Governments and private companies accelerated the rollout of fast-charging networks, though standardization (e.g., NACS adoption in North America) became a major talking point.\n", "*   **Software as a Differentiator:** In-car technology, over-the-air (OTA) updates, and advanced driver-assistance systems (ADAS) became key competitive battlegrounds.\n", "\n", "---\n", "<Slide 5 END>\n", "<Slide 6 START>\n", "### **Slide 6: 2024 Outlook & Strategic Recommendations**\n", "\n", "---\n", "\n", "**(Image: An icon of a crystal ball showing a glowing electric car inside.)**\n", "\n", "---\n", "\n", "The momentum from 2023 is expected to continue, presenting both opportunities and challenges.\n", "\n", "**Market Outlook for 2024:**\n", "\n", "*   **Sustained Growth:** Expect another year of 25-30% growth globally, although potentially at a slower pace in some mature markets.\n", "*   **The \"Affordability\" Push:** A major focus for automakers will be launching more models in the sub-$35,000 price range to drive mass-market adoption.\n", "*   **Battery Supply Chain:** Geopolitical factors and competition for raw materials (lithium, cobalt) will remain a critical area of focus.\n", "\n", "**Strategic Recommendations:**\n", "\n", "1.  **Invest in Charging Solutions:** For infrastructure players, focus on reliability and expanding high-speed charging in underserved areas.\n", "2.  **Focus on Supply Chain Resilience:** Automakers must continue to diversify sourcing for batteries and critical components.\n", "3.  **Monitor Emerging Competitors:** Keep a close watch on new, agile EV startups and their impact on market dynamics.\n", "\n", "---\n", "<Slide 6 END>\n", "\n", "Create the slide in full HTML and Tailwind CSS.\n", "\n", "Slide content to include:\n", "{'text': '<Slide 1 START>\\n### **Slide 1: Title Slide**\\n\\n**(Image: A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it.)**\\n\\n---\\n\\n**The Electric Vehicle Market: 2023 Year in Review**\\n\\n**A Data-Driven Analysis of Key Trends and Future Outlook**\\n\\n**Prepared for:** [Client Name]\\n\\n**Prepared by:** [Your Name/Consultancy]\\n\\n**Date:** October 26, 2023\\n\\n---\\n<Slide 1 END>\\n<Slide 2 START>\\n### **Slide 2: Executive Summary**\\n\\n---\\n\\nThe global Electric Vehicle (EV) market experienced unprecedented growth in 2023, driven by government incentives, technological advancements, and increasing consumer demand for sustainable transportation. This presentation provides a concise overview of the key market dynamics, sales leaders, and emerging trends from the past year.\\n\\n**Key Highlights from 2023:**\\n\\n*   **Record-Breaking Sales:** Global EV sales surged by over 30%, with key markets in Asia and Europe leading the charge.\\n*   **Intensified Competition:** While established players maintained significant market share, new entrants and legacy automakers accelerated their EV offerings, increasing competition.\\n*   **Infrastructure Expansion:** Significant investments were made in expanding the global charging infrastructure, though it remains a critical factor for mass adoption.\\n*   **Future Outlook:** The market is poised for continued growth, with a strong focus on battery innovation, software-defined vehicles, and affordability.\\n\\n---\\n<Slide 2 END>\\n<Slide 3 START>\\n### **Slide 3: 2023 Global Sales Snapshot**\\n\\n---\\n\\n**(Image: A world map with three key regions highlighted: North America, Europe, and East Asia. Each region has a large, bold percentage number next to it indicating its share of the global EV market (e.g., East Asia: 58%, Europe: 25%, North America: 12%). A small EV icon is placed within each highlighted region.)**\\n\\n---\\n\\nGlobal EV sales demonstrated robust growth, though adoption rates varied significantly by region.\\n\\n| Region | 2023 Sales (Units) | Year-over-Year Growth | Key Market Drivers |\\n| :--- | :--- | :--- | :--- |\\n| **Asia-Pacific** | ~7.8 Million | +30% | Strong government subsidies, domestic manufacturing |\\n| **Europe** | ~3.2 Million | +28% | Strict emission regulations, wide model availability |\\n| **North America** | ~1.4 Million | +45% | Federal tax credits, new model launches (trucks/SUVs) |\\n| **Rest of World** | ~0.6 Million | +25% | Nascent market growth, initial infrastructure build-out |\\n\\n*Note: Sales figures are approximate based on preliminary full-year data.*\\n\\n---\\n<Slide 3 END>\\n<Slide 4 START>\\n### **Slide 4: Competitive Landscape: Market Leaders**\\n\\n---\\n\\n**(Image: A simple bar chart titled \"Top 5 Global EV Manufacturers by Market Share (2023)\". The Y-axis is \"Market Share (%)\" and the X-axis lists the top 5 company names. Each bar is a different color and has a corresponding logo next to the name on the X-axis. For example: Tesla (Bar at ~18%), BYD (Bar at ~16%), Volkswagen Group (Bar at ~9%), etc.)**\\n\\n---\\n\\nThe competitive landscape in 2023 was a tale of two titans leading the pack, with legacy automakers and new challengers vying for position.\\n\\n1.  **Tesla:** Maintained its global leadership position, driven by the popularity of the Model Y and strategic price adjustments.\\n2.  **BYD:** Showcased explosive growth, particularly in the Asian market, challenging for the top spot with a diverse portfolio of affordable EVs and plug-in hybrids.\\n3.  **Volkswagen Group:** Solidified its position as a top contender with a broad range of EVs across its various brands (VW, Audi, Skoda).\\n4.  **Geely-Volvo:** Leveraged its multi-brand strategy (Volvo, Polestar, Zeekr) to capture significant market share, especially in the premium segment.\\n5.  **Hyundai/Kia:** Continued to gain market share with critically acclaimed and popular models like the IONIQ 5 and EV6.\\n\\n---\\n<Slide 4 END>\\n<Slide 5 START>\\n### **Slide 5: Key Trends That Shaped 2023**\\n\\n---\\n\\n**(Image: A diagram with a central circle labeled \"2023 EV Trends\". Four arrows point outwards from this circle to four smaller icons, each with a label.)**\\n*   **Icon 1: A battery with a \"down arrow\" on price.** Label: **Battery Cost Reduction**\\n*   **Icon 2: A pickup truck silhouette.** Label: **Segment Diversification**\\n*   **Icon 3: A charging plug.** Label: **Charging Infrastructure Race**\\n*   **Icon 4: A computer chip.** Label: **Software & Autonomy**\\n\\n---\\n\\nBeyond sales figures, four critical trends defined the EV market\\'s trajectory last year.\\n\\n*   **Battery Technology & Cost:** The average cost per kWh continued to fall, making EVs more affordable. Focus intensified on new chemistries like LFP (Lithium Iron Phosphate) for standard-range models.\\n*   **Diversification of Segments:** The market expanded beyond sedans. 2023 saw a significant rise in the availability and sales of electric SUVs and pickup trucks, appealing to a broader consumer base.\\n*   **The Charging Infrastructure Race:** Governments and private companies accelerated the rollout of fast-charging networks, though standardization (e.g., NACS adoption in North America) became a major talking point.\\n*   **Software as a Differentiator:** In-car technology, over-the-air (OTA) updates, and advanced driver-assistance systems (ADAS) became key competitive battlegrounds.\\n\\n---\\n<Slide 5 END>\\n<Slide 6 START>\\n### **Slide 6: 2024 Outlook & Strategic Recommendations**\\n\\n---\\n\\n**(Image: An icon of a crystal ball showing a glowing electric car inside.)**\\n\\n---\\n\\nThe momentum from 2023 is expected to continue, presenting both opportunities and challenges.\\n\\n**Market Outlook for 2024:**\\n\\n*   **Sustained Growth:** Expect another year of 25-30% growth globally, although potentially at a slower pace in some mature markets.\\n*   **The \"Affordability\" Push:** A major focus for automakers will be launching more models in the sub-$35,000 price range to drive mass-market adoption.\\n*   **Battery Supply Chain:** Geopolitical factors and competition for raw materials (lithium, cobalt) will remain a critical area of focus.\\n\\n**Strategic Recommendations:**\\n\\n1.  **Invest in Charging Solutions:** For infrastructure players, focus on reliability and expanding high-speed charging in underserved areas.\\n2.  **Focus on Supply Chain Resilience:** Automakers must continue to diversify sourcing for batteries and critical components.\\n3.  **Monitor Emerging Competitors:** Keep a close watch on new, agile EV startups and their impact on market dynamics.\\n\\n---\\n<Slide 6 END>', 'full_response': GenerateContentResponse(\n", "  automatic_function_calling_history=[],\n", "  candidates=[\n", "    Candidate(\n", "      content=Content(\n", "        parts=[\n", "          Part(\n", "            text=\"\"\"<Slide 1 START>\n", "### **Slide 1: Title Slide**\n", "\n", "**(Image: A sleek, futuristic image of an electric vehicle charging, with glowing blue lines representing data and energy flow around it.)**\n", "\n", "---\n", "\n", "**The Electric Vehicle Market: 2023 Year in Review**\n", "\n", "**A Data-Driven Analysis of Key Trends and Future Outlook**\n", "\n", "**Prepared for:** [Client Name]\n", "\n", "**Prepared by:** [Your Name/Consultancy]\n", "\n", "**Date:** October 26, 2023\n", "\n", "---\n", "<Slide 1 END>\n", "<Slide 2 START>\n", "### **Slide 2: Executive Summary**\n", "\n", "---\n", "\n", "The global Electric Vehicle (EV) market experienced unprecedented growth in 2023, driven by government incentives, technological advancements, and increasing consumer demand for sustainable transportation. This presentation provides a concise overview of the key market dynamics, sales leaders, and emerging trends from the past year.\n", "\n", "**Key Highlights from 2023:**\n", "\n", "*   **Record-Breaking Sales:** Global EV sales surged by over 30%, with key markets in Asia and Europe leading the charge.\n", "*   **Intensified Competition:** While established players maintained significant market share, new entrants and legacy automakers accelerated their EV offerings, increasing competition.\n", "*   **Infrastructure Expansion:** Significant investments were made in expanding the global charging infrastructure, though it remains a critical factor for mass adoption.\n", "*   **Future Outlook:** The market is poised for continued growth, with a strong focus on battery innovation, software-defined vehicles, and affordability.\n", "\n", "---\n", "<Slide 2 END>\n", "<Slide 3 START>\n", "### **Slide 3: 2023 Global Sales Snapshot**\n", "\n", "---\n", "\n", "**(Image: A world map with three key regions highlighted: North America, Europe, and East Asia. Each region has a large, bold percentage number next to it indicating its share of the global EV market (e.g., East Asia: 58%, Europe: 25%, North America: 12%). A small EV icon is placed within each highlighted region.)**\n", "\n", "---\n", "\n", "Global EV sales demonstrated robust growth, though adoption rates varied significantly by region.\n", "\n", "| Region | 2023 Sales (Units) | Year-over-Year Growth | Key Market Drivers |\n", "| :--- | :--- | :--- | :--- |\n", "| **Asia-Pacific** | ~7.8 Million | +30% | Strong government subsidies, domestic manufacturing |\n", "| **Europe** | ~3.2 Million | +28% | Strict emission regulations, wide model availability |\n", "| **North America** | ~1.4 Million | +45% | Federal tax credits, new model launches (trucks/SUVs) |\n", "| **Rest of World** | ~0.6 Million | +25% | Nascent market growth, initial infrastructure build-out |\n", "\n", "*Note: Sales figures are approximate based on preliminary full-year data.*\n", "\n", "---\n", "<Slide 3 END>\n", "<Slide 4 START>\n", "### **Slide 4: Competitive Landscape: Market Leaders**\n", "\n", "---\n", "\n", "**(Image: A simple bar chart titled \"Top 5 Global EV Manufacturers by Market Share (2023)\". The Y-axis is \"Market Share (%)\" and the X-axis lists the top 5 company names. Each bar is a different color and has a corresponding logo next to the name on the X-axis. For example: Tesla (Bar at ~18%), BYD (Bar at ~16%), Volkswagen Group (Bar at ~9%), etc.)**\n", "\n", "---\n", "\n", "The competitive landscape in 2023 was a tale of two titans leading the pack, with legacy automakers and new challengers vying for position.\n", "\n", "1.  **Tesla:** Maintained its global leadership position, driven by the popularity of the Model Y and strategic price adjustments.\n", "2.  **BYD:** Showcased explosive growth, particularly in the Asian market, challenging for the top spot with a diverse portfolio of affordable EVs and plug-in hybrids.\n", "3.  **Volkswagen Group:** Solidified its position as a top contender with a broad range of EVs across its various brands (VW, Audi, Skoda).\n", "4.  **Geely-Volvo:** Leveraged its multi-brand strategy (Volvo, Polestar, Zeekr) to capture significant market share, especially in the premium segment.\n", "5.  **Hyundai/Kia:** Continued to gain market share with critically acclaimed and popular models like the IONIQ 5 and EV6.\n", "\n", "---\n", "<Slide 4 END>\n", "<Slide 5 START>\n", "### **Slide 5: Key Trends That Shaped 2023**\n", "\n", "---\n", "\n", "**(Image: A diagram with a central circle labeled \"2023 EV Trends\". Four arrows point outwards from this circle to four smaller icons, each with a label.)**\n", "*   **Icon 1: A battery with a \"down arrow\" on price.** Label: **Battery Cost Reduction**\n", "*   **Icon 2: A pickup truck silhouette.** Label: **Segment Diversification**\n", "*   **Icon 3: A charging plug.** Label: **Charging Infrastructure Race**\n", "*   **Icon 4: A computer chip.** Label: **Software & Autonomy**\n", "\n", "---\n", "\n", "Beyond sales figures, four critical trends defined the EV market's trajectory last year.\n", "\n", "*   **Battery Technology & Cost:** The average cost per kWh continued to fall, making EVs more affordable. Focus intensified on new chemistries like LFP (Lithium Iron Phosphate) for standard-range models.\n", "*   **Diversification of Segments:** The market expanded beyond sedans. 2023 saw a significant rise in the availability and sales of electric SUVs and pickup trucks, appealing to a broader consumer base.\n", "*   **The Charging Infrastructure Race:** Governments and private companies accelerated the rollout of fast-charging networks, though standardization (e.g., NACS adoption in North America) became a major talking point.\n", "*   **Software as a Differentiator:** In-car technology, over-the-air (OTA) updates, and advanced driver-assistance systems (ADAS) became key competitive battlegrounds.\n", "\n", "---\n", "<Slide 5 END>\n", "<Slide 6 START>\n", "### **Slide 6: 2024 Outlook & Strategic Recommendations**\n", "\n", "---\n", "\n", "**(Image: An icon of a crystal ball showing a glowing electric car inside.)**\n", "\n", "---\n", "\n", "The momentum from 2023 is expected to continue, presenting both opportunities and challenges.\n", "\n", "**Market Outlook for 2024:**\n", "\n", "*   **Sustained Growth:** Expect another year of 25-30% growth globally, although potentially at a slower pace in some mature markets.\n", "*   **The \"Affordability\" Push:** A major focus for automakers will be launching more models in the sub-$35,000 price range to drive mass-market adoption.\n", "*   **Battery Supply Chain:** Geopolitical factors and competition for raw materials (lithium, cobalt) will remain a critical area of focus.\n", "\n", "**Strategic Recommendations:**\n", "\n", "1.  **Invest in Charging Solutions:** For infrastructure players, focus on reliability and expanding high-speed charging in underserved areas.\n", "2.  **Focus on Supply Chain Resilience:** Automakers must continue to diversify sourcing for batteries and critical components.\n", "3.  **Monitor Emerging Competitors:** Keep a close watch on new, agile EV startups and their impact on market dynamics.\n", "\n", "---\n", "<Slide 6 END>\"\"\"\n", "          ),\n", "        ],\n", "        role='model'\n", "      ),\n", "      finish_reason=<FinishReason.STOP: 'STOP'>,\n", "      index=0\n", "    ),\n", "  ],\n", "  model_version='gemini-2.5-pro',\n", "  sdk_http_response=HttpResponse(\n", "    headers=<dict len=10>\n", "  ),\n", "  usage_metadata=GenerateContentResponseUsageMetadata(\n", "    candidates_token_count=1589,\n", "    prompt_token_count=2885,\n", "    prompt_tokens_details=[\n", "      ModalityTokenCount(\n", "        modality=<MediaModality.TEXT: 'TEXT'>,\n", "        token_count=2885\n", "      ),\n", "    ],\n", "    thoughts_token_count=112,\n", "    total_token_count=4586\n", "  )\n", ")}\n", "\n", "You need to pay EXTREME ATTENTION to the position of the elements inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS\n", "\n", "# ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS\n", "\n", "Constraints:\n", "- Conform to the design style of the existing slides for consistency\n", "- Eliminate the use of animations and transitions.\n", "- The css component describing the main content area must be called .slide-content\n", "- Titles should be aligned to the top left-hand side of the slide\n", "- The slides MUST be 720p, and ensure all slide content fits within a height of 720px.\n", "- Output only the final HTML for the new slide.\n", "\n", "Beyond all your mission is to make this BEAUTIFUL\n", "REMEMBER: You must output FULL HTML and Tailwind CSS, starting with <!DOCTYPE html> and ending with </html> and NOTHING ELSE\n", "\n"]}], "source": ["general_slide_query = generationpy_slide_prompt_v2.format(query=query, existing_slides=slide_content_response['text'], slide_content=slide_content_response)\n", "print (general_slide_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "collapsed": true, "id": "B4DLVLCMt_QD", "outputId": "4879df32-34ff-4e29-ea46-8e0f5353f7fe"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:09:54.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mGenerating a general slide...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Stored Title Slide\n", "✅ Stored Agenda Slide\n", "--- ⚙️ Generating Slide 3: ### **Slide 3: 2023 Global Sales Snapshot**\n", "\n", "---\n", "\n", "**(Image: A world map with three key regions highlighted: North America, Europe, and East Asia. Each region has a large, bold percentage number next to it indicating its share of the global EV market (e.g., East Asia: 58%, Europe: 25%, North America: 12%). A small EV icon is placed within each highlighted region.)**\n", "\n", "---\n", "\n", "Global EV sales demonstrated robust growth, though adoption rates varied significantly by region.\n", "\n", "| Region | 2023 Sales (Units) | Year-over-Year Growth | Key Market Drivers |\n", "| :--- | :--- | :--- | :--- |\n", "| **Asia-Pacific** | ~7.8 Million | +30% | Strong government subsidies, domestic manufacturing |\n", "| **Europe** | ~3.2 Million | +28% | Strict emission regulations, wide model availability |\n", "| **North America** | ~1.4 Million | +45% | Federal tax credits, new model launches (trucks/SUVs) |\n", "| **Rest of World** | ~0.6 Million | +25% | Nascent market growth, initial infrastructure build-out |\n", "\n", "*Note: Sales figures are approximate based on preliminary full-year data.*\n", "\n", "--- ---\n", "🔍Tavily Searching for: 'world map with highlighted regions for data visualization, clean and modern style'\n", "✅ Successfully generated slide 3. Displaying below:\n"]}, {"data": {"text/html": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>2023 Global Sales Snapshot</title>\n", "    <script src=\"https://cdn.tailwindcss.com\"></script>\n", "    <style>\n", "        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');\n", "        body {\n", "            font-family: 'Inter', sans-serif;\n", "            background-color: #f3f4f6;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"w-[1280px] h-[720px] bg-white shadow-lg mx-auto my-8 flex flex-col\">\n", "        <div class=\"slide-content w-full h-full p-16 flex flex-col\">\n", "            <h1 class=\"text-5xl font-bold text-gray-800 mb-2\">2023 Global Sales Snapshot</h1>\n", "            <p class=\"text-xl text-gray-500 mb-8\">Global EV sales demonstrated robust growth, though adoption rates varied significantly by region.</p>\n", "            \n", "            <div class=\"flex-grow w-full flex flex-col justify-center items-center space-y-8\">\n", "                <!-- Image Section -->\n", "                <div class=\"relative w-full max-w-5xl mx-auto\">\n", "                    <img src=\"https://img.freepik.com/premium-photo/digital-world-map-with-highlighted-regions-data-points_230115-14072.jpg?w=1480\" alt=\"World map with highlighted regions for EV sales\" class=\"w-full h-auto object-cover rounded-lg shadow-md opacity-80\">\n", "                    <!-- Data Overlays -->\n", "                    <div class=\"absolute top-[35%] left-[5%] text-white text-center\">\n", "                        <p class=\"text-4xl font-bold text-shadow-lg\">12%</p>\n", "                        <p class=\"text-lg font-semibold\">North America</p>\n", "                    </div>\n", "                    <div class=\"absolute top-[20%] left-[45%] text-white text-center\">\n", "                        <p class=\"text-4xl font-bold text-shadow-lg\">25%</p>\n", "                        <p class=\"text-lg font-semibold\">Europe</p>\n", "                    </div>\n", "                    <div class=\"absolute top-[40%] left-[75%] text-white text-center\">\n", "                        <p class=\"text-4xl font-bold text-shadow-lg\">58%</p>\n", "                        <p class=\"text-lg font-semibold\">East Asia</p>\n", "                    </div>\n", "                </div>\n", "\n", "                <!-- Table Section -->\n", "                <div class=\"w-full max-w-6xl mx-auto pt-4\">\n", "                    <table class=\"min-w-full bg-white border-collapse\">\n", "                        <thead class=\"bg-gray-50\">\n", "                            <tr>\n", "                                <th class=\"text-left py-3 px-4 font-semibold text-gray-600 uppercase tracking-wider border-b-2 border-gray-200\">Region</th>\n", "                                <th class=\"text-left py-3 px-4 font-semibold text-gray-600 uppercase tracking-wider border-b-2 border-gray-200\">2023 Sales (Units)</th>\n", "                                <th class=\"text-left py-3 px-4 font-semibold text-gray-600 uppercase tracking-wider border-b-2 border-gray-200\">Year-over-Year Growth</th>\n", "                                <th class=\"text-left py-3 px-4 font-semibold text-gray-600 uppercase tracking-wider border-b-2 border-gray-200\">Key Market Drivers</th>\n", "                            </tr>\n", "                        </thead>\n", "                        <tbody class=\"text-gray-700\">\n", "                            <tr class=\"hover:bg-gray-50\">\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 font-bold\">Asia-Pacific</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">~7.8 Million</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 text-green-600 font-semibold\">+30%</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">Strong government subsidies, domestic manufacturing</td>\n", "                            </tr>\n", "                            <tr class=\"hover:bg-gray-50\">\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 font-bold\">Europe</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">~3.2 Million</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 text-green-600 font-semibold\">+28%</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">Strict emission regulations, wide model availability</td>\n", "                            </tr>\n", "                            <tr class=\"hover:bg-gray-50\">\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 font-bold\">North America</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">~1.4 Million</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200 text-green-600 font-semibold\">+45%</td>\n", "                                <td class=\"py-3 px-4 border-b border-gray-200\">Federal tax credits, new model launches (trucks/SUVs)</td>\n", "                            </tr>\n", "                            <tr class=\"hover:bg-gray-50\">\n", "                                <td class=\"py-3 px-4 font-bold\">Rest of World</td>\n", "                                <td class=\"py-3 px-4\">~0.6 Million</td>\n", "                                <td class=\"py-3 px-4 text-green-600 font-semibold\">+25%</td>\n", "                                <td class=\"py-3 px-4\">Nascent market growth, initial infrastructure build-out</td>\n", "                            </tr>\n", "                        </tbody>\n", "                    </table>\n", "                </div>\n", "            </div>\n", "            <p class=\"text-sm text-gray-400 mt-4 text-right w-full max-w-6xl mx-auto\">*Note: Sales figures are approximate based on preliminary full-year data.</p>\n", "        </div>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:10:23.928\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mGenerating a general slide...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\\n==================================================\\n\n", "--- ⚙️ Generating Slide 4: ### **Slide 4: Competitive Landscape: Market Leaders**\n", "\n", "---\n", "\n", "**(Image: A simple bar chart titled \"Top 5 Global EV Manufacturers by Market Share (2023)\". The Y-axis is \"Market Share (%)\" and the X-axis lists the top 5 company names. Each bar is a different color and has a corresponding logo next to the name on the X-axis. For example: Tesla (Bar at ~18%), BYD (Bar at ~16%), Volkswagen Group (Bar at ~9%), etc.)**\n", "\n", "---\n", "\n", "The competitive landscape in 2023 was a tale of two titans leading the pack, with legacy automakers and new challengers vying for position.\n", "\n", "1.  **Tesla:** Maintained its global leadership position, driven by the popularity of the Model Y and strategic price adjustments.\n", "2.  **BYD:** Showcased explosive growth, particularly in the Asian market, challenging for the top spot with a diverse portfolio of affordable EVs and plug-in hybrids.\n", "3.  **Volkswagen Group:** Solidified its position as a top contender with a broad range of EVs across its various brands (VW, Audi, Skoda).\n", "4.  **Geely-Volvo:** Leveraged its multi-brand strategy (Volvo, Polestar, Zeekr) to capture significant market share, especially in the premium segment.\n", "5.  **Hyundai/Kia:** Continued to gain market share with critically acclaimed and popular models like the IONIQ 5 and EV6.\n", "\n", "--- ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:10:27.578\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'Tesla logo'\u001b[0m\n", "/tmp/ipython-input-1836134849.py:54: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_result = qdrant_client.search(\n", "\u001b[32m2025-08-04 06:10:27.898\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'Tesla logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:27.899\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'BYD logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.023\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'BYD logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.024\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'Volkswagen Group logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.154\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'Volkswagen Group logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.155\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'Geely logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.282\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'Geely logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.283\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'Hyundai Kia logo'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:28.403\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'Hyundai Kia logo'\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully generated slide 4. Displaying below:\n"]}, {"data": {"text/html": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>Competitive Landscape: Market Leaders</title>\n", "    <script src=\"https://cdn.tailwindcss.com\"></script>\n", "    <style>\n", "        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');\n", "        body {\n", "            font-family: 'Inter', sans-serif;\n", "            background-color: #f3f4f6;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"w-[1280px] h-[720px] bg-white shadow-lg mx-auto my-8 flex flex-col\">\n", "        <div class=\"slide-content w-full h-full p-16 flex flex-col\">\n", "            <h1 class=\"text-5xl font-bold text-gray-800 mb-2\">Competitive Landscape: Market Leaders</h1>\n", "            <p class=\"text-xl text-gray-500 mb-8\">The 2023 landscape was a tale of two titans leading, with others vying for position.</p>\n", "            \n", "            <div class=\"flex-grow w-full flex items-center gap-x-12\">\n", "                <!-- Bar Chart Section -->\n", "                <div class=\"w-1/2 h-full flex flex-col justify-center\">\n", "                    <div class=\"space-y-6\">\n", "                        <!-- <PERSON><PERSON> -->\n", "                        <div class=\"flex items-center\">\n", "                            <div class=\"w-32 flex-shrink-0 flex items-center gap-x-3\">\n", "                                <img src=\"https://www.svgrepo.com/show/306567/tesla.svg\" alt=\"Tesla Logo\" class=\"h-7 w-7\">\n", "                                <span class=\"font-semibold text-gray-700 text-lg\">Tesla</span>\n", "                            </div>\n", "                            <div class=\"flex-grow bg-gray-200 rounded-full h-8\">\n", "                                <div class=\"bg-red-500 h-8 rounded-full flex items-center justify-center\" style=\"width: 72%;\">\n", "                                    <span class=\"text-white font-bold text-sm\">18%</span>\n", "                                </div>\n", "                            </div>\n", "                        </div>\n", "                        <!-- BYD -->\n", "                        <div class=\"flex items-center\">\n", "                            <div class=\"w-32 flex-shrink-0 flex items-center gap-x-3\">\n", "                                <img src=\"https://www.svgrepo.com/show/331393/byd-logo.svg\" alt=\"BYD Logo\" class=\"h-7 w-7\">\n", "                                <span class=\"font-semibold text-gray-700 text-lg\">BYD</span>\n", "                            </div>\n", "                            <div class=\"flex-grow bg-gray-200 rounded-full h-8\">\n", "                                <div class=\"bg-blue-500 h-8 rounded-full flex items-center justify-center\" style=\"width: 64%;\">\n", "                                    <span class=\"text-white font-bold text-sm\">16%</span>\n", "                                </div>\n", "                            </div>\n", "                        </div>\n", "                        <!-- Volkswagen Group -->\n", "                        <div class=\"flex items-center\">\n", "                            <div class=\"w-32 flex-shrink-0 flex items-center gap-x-3\">\n", "                                <img src=\"https://www.svgrepo.com/show/349545/volkswagen.svg\" alt=\"Volkswagen Logo\" class=\"h-7 w-7\">\n", "                                <span class=\"font-semibold text-gray-700 text-lg\">VW Group</span>\n", "                            </div>\n", "                            <div class=\"flex-grow bg-gray-200 rounded-full h-8\">\n", "                                <div class=\"bg-teal-500 h-8 rounded-full flex items-center justify-center\" style=\"width: 36%;\">\n", "                                    <span class=\"text-white font-bold text-sm\">9%</span>\n", "                                </div>\n", "                            </div>\n", "                        </div>\n", "                        <!-- <PERSON><PERSON>-Volvo -->\n", "                        <div class=\"flex items-center\">\n", "                            <div class=\"w-32 flex-shrink-0 flex items-center gap-x-3\">\n", "                                <img src=\"https://www.svgrepo.com/show/521553/volvo.svg\" alt=\"Geely-Volvo Logo\" class=\"h-7 w-7\">\n", "                                <span class=\"font-semibold text-gray-700 text-lg\">Geely-Volvo</span>\n", "                            </div>\n", "                            <div class=\"flex-grow bg-gray-200 rounded-full h-8\">\n", "                                <div class=\"bg-gray-600 h-8 rounded-full flex items-center justify-center\" style=\"width: 28%;\">\n", "                                    <span class=\"text-white font-bold text-sm\">7%</span>\n", "                                </div>\n", "                            </div>\n", "                        </div>\n", "                        <!-- Hyundai/Kia -->\n", "                        <div class=\"flex items-center\">\n", "                            <div class=\"w-32 flex-shrink-0 flex items-center gap-x-3\">\n", "                                <img src=\"https://www.svgrepo.com/show/349398/kia.svg\" alt=\"Hyundai/Kia Logo\" class=\"h-7 w-7\">\n", "                                <span class=\"font-semibold text-gray-700 text-lg\">Hyundai/Kia</span>\n", "                            </div>\n", "                            <div class=\"flex-grow bg-gray-200 rounded-full h-8\">\n", "                                <div class=\"bg-orange-500 h-8 rounded-full flex items-center justify-center\" style=\"width: 24%;\">\n", "                                    <span class=\"text-white font-bold text-sm\">6%</span>\n", "                                </div>\n", "                            </div>\n", "                        </div>\n", "                    </div>\n", "                    <p class=\"text-center text-gray-500 mt-4 text-sm font-semibold\">Top 5 Global EV Manufacturers by Market Share (2023)</p>\n", "                </div>\n", "\n", "                <!-- Text Content Section -->\n", "                <div class=\"w-1/2\">\n", "                    <ul class=\"space-y-5 text-gray-700 text-lg\">\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-red-500 font-bold text-2xl mr-4 mt-1\">&#9679;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Tesla:</strong> Maintained global leadership, driven by the Model Y's popularity and strategic price adjustments.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-blue-500 font-bold text-2xl mr-4 mt-1\">&#9679;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">BYD:</strong> Showcased explosive growth, particularly in Asia, challenging for the top spot with a diverse, affordable portfolio.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-teal-500 font-bold text-2xl mr-4 mt-1\">&#9679;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Volkswagen Group:</strong> Solidified its position with a broad range of EVs across its brands (VW, Audi, Skoda).</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-gray-600 font-bold text-2xl mr-4 mt-1\">&#9679;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Geely-Volvo:</strong> Leveraged its multi-brand strategy (Volvo, Polestar, Zeekr) to capture significant share in the premium segment.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-orange-500 font-bold text-2xl mr-4 mt-1\">&#9679;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Hyundai/Kia:</strong> Continued to gain market share with critically acclaimed models like the IONIQ 5 and EV6.</div>\n", "                        </li>\n", "                    </ul>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:10:47.736\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mGenerating a general slide...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\\n==================================================\\n\n", "--- ⚙️ Generating Slide 5: ### **Slide 5: Key Trends That Shaped 2023**\n", "\n", "---\n", "\n", "**(Image: A diagram with a central circle labeled \"2023 EV Trends\". Four arrows point outwards from this circle to four smaller icons, each with a label.)**\n", "*   **Icon 1: A battery with a \"down arrow\" on price.** Label: **Battery Cost Reduction**\n", "*   **Icon 2: A pickup truck silhouette.** Label: **Segment Diversification**\n", "*   **Icon 3: A charging plug.** Label: **Charging Infrastructure Race**\n", "*   **Icon 4: A computer chip.** Label: **Software & Autonomy**\n", "\n", "---\n", "\n", "Beyond sales figures, four critical trends defined the EV market's trajectory last year.\n", "\n", "*   **Battery Technology & Cost:** The average cost per kWh continued to fall, making EVs more affordable. Focus intensified on new chemistries like LFP (Lithium Iron Phosphate) for standard-range models.\n", "*   **Diversification of Segments:** The market expanded beyond sedans. 2023 saw a significant rise in the availability and sales of electric SUVs and pickup trucks, appealing to a broader consumer base.\n", "*   **The Charging Infrastructure Race:** Governments and private companies accelerated the rollout of fast-charging networks, though standardization (e.g., NACS adoption in North America) became a major talking point.\n", "*   **Software as a Differentiator:** In-car technology, over-the-air (OTA) updates, and advanced driver-assistance systems (ADAS) became key competitive battlegrounds.\n", "\n", "--- ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:10:50.716\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'battery with a down arrow'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.023\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'battery with a down arrow'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.024\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'pickup truck silhouette'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.153\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'pickup truck silhouette'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.154\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'charging plug'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.275\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'charging plug'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.276\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'computer chip'\u001b[0m\n", "\u001b[32m2025-08-04 06:10:51.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'computer chip'\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully generated slide 5. Displaying below:\n"]}, {"data": {"text/html": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>Key Trends That Shaped 2023</title>\n", "    <script src=\"https://cdn.tailwindcss.com\"></script>\n", "    <style>\n", "        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');\n", "        body {\n", "            font-family: 'Inter', sans-serif;\n", "            background-color: #f3f4f6;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"w-[1280px] h-[720px] bg-white shadow-lg mx-auto my-8 flex flex-col\">\n", "        <div class=\"slide-content w-full h-full p-16 flex flex-col\">\n", "            <h1 class=\"text-5xl font-bold text-gray-800 mb-2\">Key Trends That Shaped 2023</h1>\n", "            <p class=\"text-xl text-gray-500 mb-10\">Beyond sales figures, four critical trends defined the EV market's trajectory last year.</p>\n", "            \n", "            <div class=\"flex-grow w-full flex items-center gap-x-12\">\n", "                <!-- Diagram Section -->\n", "                <div class=\"w-2/5 h-full flex flex-col justify-center items-center\">\n", "                    <div class=\"relative w-96 h-96\">\n", "                        <div class=\"absolute inset-0 flex items-center justify-center\">\n", "                            <div class=\"w-48 h-48 bg-blue-500 rounded-full flex items-center justify-center shadow-lg\">\n", "                                <p class=\"text-white text-2xl font-bold text-center leading-tight\">2023<br>EV Trends</p>\n", "                            </div>\n", "                        </div>\n", "                        \n", "                        <!-- Trend 1: Battery Cost -->\n", "                        <div class=\"absolute top-0 left-1/2 -translate-x-1/2 -translate-y-8 transform\">\n", "                            <div class=\"flex flex-col items-center text-center\">\n", "                                <div class=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center shadow-md border border-gray-200\">\n", "                                    <img src=\"https://www.svgrepo.com/show/494740/battery-bolt.svg\" alt=\"Battery Icon\" class=\"h-8 w-8\">\n", "                                </div>\n", "                                <p class=\"mt-2 font-semibold text-gray-700\">Battery Cost Reduction</p>\n", "                            </div>\n", "                        </div>\n", "                        \n", "                        <!-- Trend 2: Segment Diversification -->\n", "                        <div class=\"absolute right-0 top-1/2 -translate-y-1/2 translate-x-8 transform\">\n", "                            <div class=\"flex flex-col items-center text-center\">\n", "                                <div class=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center shadow-md border border-gray-200\">\n", "                                    <img src=\"https://www.svgrepo.com/show/492637/truck.svg\" alt=\"Pickup Truck Icon\" class=\"h-8 w-8\">\n", "                                </div>\n", "                                <p class=\"mt-2 font-semibold text-gray-700\">Segment Diversification</p>\n", "                            </div>\n", "                        </div>\n", "                        \n", "                        <!-- Trend 3: Charging Infrastructure -->\n", "                        <div class=\"absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-8 transform\">\n", "                            <div class=\"flex flex-col items-center text-center\">\n", "                                <div class=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center shadow-md border border-gray-200\">\n", "                                    <img src=\"https://www.svgrepo.com/show/503925/charger.svg\" alt=\"Charging Plug Icon\" class=\"h-8 w-8\">\n", "                                </div>\n", "                                <p class=\"mt-2 font-semibold text-gray-700\">Charging Infrastructure</p>\n", "                            </div>\n", "                        </div>\n", "\n", "                        <!-- Trend 4: Software & Autonomy -->\n", "                        <div class=\"absolute left-0 top-1/2 -translate-y-1/2 -translate-x-8 transform\">\n", "                            <div class=\"flex flex-col items-center text-center\">\n", "                                <div class=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center shadow-md border border-gray-200\">\n", "                                    <img src=\"https://www.svgrepo.com/show/419146/chip-cpu-hardware.svg\" alt=\"Computer Chip Icon\" class=\"h-8 w-8\">\n", "                                </div>\n", "                                <p class=\"mt-2 font-semibold text-gray-700\">Software & Autonomy</p>\n", "                            </div>\n", "                        </div>\n", "                    </div>\n", "                </div>\n", "\n", "                <!-- Text Content Section -->\n", "                <div class=\"w-3/5\">\n", "                    <ul class=\"space-y-6 text-gray-700 text-lg\">\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-blue-500 text-2xl mr-4 mt-1\">&#9670;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Battery Technology & Cost:</strong> The average cost per kWh continued to fall, making EVs more affordable. Focus intensified on new chemistries like LFP for standard-range models.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-blue-500 text-2xl mr-4 mt-1\">&#9670;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Diversification of Segments:</strong> The market expanded beyond sedans. 2023 saw a significant rise in sales of electric SUVs and pickup trucks, appealing to a broader consumer base.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-blue-500 text-2xl mr-4 mt-1\">&#9670;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">The Charging Infrastructure Race:</strong> Rollout of fast-charging networks accelerated, though standardization (e.g., NACS adoption) became a major talking point.</div>\n", "                        </li>\n", "                        <li class=\"flex items-start\">\n", "                            <span class=\"text-blue-500 text-2xl mr-4 mt-1\">&#9670;</span>\n", "                            <div><strong class=\"font-semibold text-gray-800\">Software as a Differentiator:</strong> In-car technology, over-the-air (OTA) updates, and advanced driver-assistance systems (ADAS) became key competitive battlegrounds.</div>\n", "                        </li>\n", "                    </ul>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:11:08.410\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mGenerating a general slide...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\\n==================================================\\n\n", "--- ⚙️ Generating Slide 6: ### **Slide 6: 2024 Outlook & Strategic Recommendations**\n", "\n", "---\n", "\n", "**(Image: An icon of a crystal ball showing a glowing electric car inside.)**\n", "\n", "---\n", "\n", "The momentum from 2023 is expected to continue, presenting both opportunities and challenges.\n", "\n", "**Market Outlook for 2024:**\n", "\n", "*   **Sustained Growth:** Expect another year of 25-30% growth globally, although potentially at a slower pace in some mature markets.\n", "*   **The \"Affordability\" Push:** A major focus for automakers will be launching more models in the sub-$35,000 price range to drive mass-market adoption.\n", "*   **Battery Supply Chain:** Geopolitical factors and competition for raw materials (lithium, cobalt) will remain a critical area of focus.\n", "\n", "**Strategic Recommendations:**\n", "\n", "1.  **Invest in Charging Solutions:** For infrastructure players, focus on reliability and expanding high-speed charging in underserved areas.\n", "2.  **Focus on Supply Chain Resilience:** Automakers must continue to diversify sourcing for batteries and critical components.\n", "3.  **Monitor Emerging Competitors:** Keep a close watch on new, agile EV startups and their impact on market dynamics.\n", "\n", "--- ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-08-04 06:11:11.822\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m51\u001b[0m - \u001b[1mSearching for logos/icons similar to: 'crystal ball with a car inside'\u001b[0m\n", "\u001b[32m2025-08-04 06:11:12.194\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36msearch_similar_logos_qdrant\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mFound 5 results for query 'crystal ball with a car inside'\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully generated slide 6. Displaying below:\n"]}, {"data": {"text/html": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <title>2024 Outlook & Strategic Recommendations</title>\n", "    <script src=\"https://cdn.tailwindcss.com\"></script>\n", "    <style>\n", "        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');\n", "        body {\n", "            font-family: 'Inter', sans-serif;\n", "            background-color: #f3f4f6;\n", "        }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"w-[1280px] h-[720px] bg-white shadow-lg mx-auto my-8 flex flex-col\">\n", "        <div class=\"slide-content w-full h-full p-16 flex flex-col\">\n", "            <h1 class=\"text-5xl font-bold text-gray-800 mb-2\">2024 Outlook & Strategic Recommendations</h1>\n", "            <p class=\"text-xl text-gray-500 mb-10\">The momentum from 2023 is expected to continue, presenting both opportunities and challenges.</p>\n", "            \n", "            <div class=\"flex-grow w-full flex items-center gap-x-16\">\n", "                <!-- Image Section -->\n", "                <div class=\"w-1/3 h-full flex flex-col justify-center items-center px-4\">\n", "                    <div class=\"relative w-64 h-64\">\n", "                        <div class=\"absolute inset-0 bg-blue-100 rounded-full blur-2xl opacity-50\"></div>\n", "                        <div class=\"absolute inset-0 border-4 border-blue-200 rounded-full\"></div>\n", "                        <div class=\"absolute inset-2 border-2 border-blue-200 rounded-full opacity-75\"></div>\n", "                        <div class=\"absolute inset-0 flex items-center justify-center\">\n", "                            <img src=\"https://www.svgrepo.com/show/513282/car.svg\" alt=\"Electric Car Icon\" class=\"h-32 w-32 text-blue-500 animate-pulse\" style=\"filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.7));\">\n", "                        </div>\n", "                        <div class=\"absolute top-1/2 left-1/2 w-56 h-56 -translate-x-1/2 -translate-y-1/2 rounded-full border-t-2 border-l-2 border-white/50 opacity-75\" style=\"transform: rotate(45deg) translateX(-50%) translateY(-50%); transform-origin: center;\"></div>\n", "                    </div>\n", "                    <p class=\"mt-6 text-2xl font-semibold text-gray-700 text-center\">Future at a Glance</p>\n", "                </div>\n", "\n", "                <!-- Text Content Section -->\n", "                <div class=\"w-2/3\">\n", "                    <div class=\"space-y-8\">\n", "                        <div>\n", "                            <h2 class=\"text-3xl font-bold text-gray-800 mb-4 border-b-2 border-blue-200 pb-2\">Market Outlook for 2024</h2>\n", "                            <ul class=\"space-y-4 text-gray-700 text-lg list-disc list-inside\">\n", "                                <li><strong class=\"font-semibold\">Sustained Growth:</strong> Expect another year of 25-30% growth globally, although potentially at a slower pace in some mature markets.</li>\n", "                                <li><strong class=\"font-semibold\">The \"Affordability\" Push:</strong> A major focus will be launching more models in the sub-$35,000 price range to drive mass-market adoption.</li>\n", "                                <li><strong class=\"font-semibold\">Battery Supply Chain:</strong> Geopolitical factors and competition for raw materials will remain a critical area of focus.</li>\n", "                            </ul>\n", "                        </div>\n", "                        <div>\n", "                            <h2 class=\"text-3xl font-bold text-gray-800 mb-4 border-b-2 border-green-200 pb-2\">Strategic Recommendations</h2>\n", "                            <ul class=\"space-y-4 text-gray-700 text-lg\">\n", "                                <li class=\"flex items-start\">\n", "                                    <span class=\"bg-green-500 text-white rounded-full w-7 h-7 text-center font-bold mr-4 flex-shrink-0 mt-1\">1</span>\n", "                                    <div><strong class=\"font-semibold\">Invest in Charging Solutions:</strong> For infrastructure players, focus on reliability and expanding high-speed charging in underserved areas.</div>\n", "                                </li>\n", "                                <li class=\"flex items-start\">\n", "                                    <span class=\"bg-green-500 text-white rounded-full w-7 h-7 text-center font-bold mr-4 flex-shrink-0 mt-1\">2</span>\n", "                                    <div><strong class=\"font-semibold\">Focus on Supply Chain Resilience:</strong> Automakers must continue to diversify sourcing for batteries and critical components.</div>\n", "                                </li>\n", "                                <li class=\"flex items-start\">\n", "                                    <span class=\"bg-green-500 text-white rounded-full w-7 h-7 text-center font-bold mr-4 flex-shrink-0 mt-1\">3</span>\n", "                                    <div><strong class=\"font-semibold\">Monitor Emerging Competitors:</strong> Keep a close watch on new, agile EV startups and their impact on market dynamics.</div>\n", "                                </li>\n", "                            </ul>\n", "                        </div>\n", "                    </div>\n", "                </div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "</body>\n", "</html>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\\n==================================================\\n\n", "🎉🎉🎉 All slides have been generated successfully! 🎉🎉🎉\n"]}], "source": ["# (This cell replaces your last three cells)\n", "\n", "import re\n", "from IPython.display import display, HTML\n", "\n", "# 1. Initialize a list to store all our final slide data\n", "all_slides_data = []\n", "\n", "# 2. Add the already generated Title Slide to our list\n", "print(\"✅ Stored Title Slide\")\n", "all_slides_data.append({'name': 'Title Slide', 'html': title_slide_html})\n", "\n", "\n", "# 3. Add the already generated Agenda Slide to our list\n", "print(\"✅ Stored Agenda Slide\")\n", "all_slides_data.append({'name': 'Agenda Slide', 'html': agenda_slide_html})\n", "\n", "\n", "# 4. Parse the outline to get the content for the remaining slides\n", "# We use regex to find all slide content blocks, excluding the first two which we've done.\n", "slide_content_text = slide_content_response['text']\n", "slides_to_generate = re.findall(r\"<Slide (\\d+) START>(.*?)<Slide \\1 END>\", slide_content_text, re.DOTALL)\n", "\n", "# We start from slide 3 since we have the Title (1) and Agenda (2)\n", "for slide_number, content in slides_to_generate[2:]:\n", "    slide_name = content.strip().split('\\\\n')[0].replace('**Slide Title:**', '').strip()\n", "    print(f\"--- ⚙️ Generating Slide {slide_number}: {slide_name} ---\")\n", "\n", "    # A. Format the context of existing slides for the prompt\n", "    existing_slides_context = \"\\\\n\".join(\n", "        [f\"--- {s['name']} ---\\\\n{s['html']}\" for s in all_slides_data]\n", "    )\n", "\n", "    # <PERSON>. Create the specific prompt for this slide\n", "    # We pass the content for the *current* slide and the HTML of *previous* slides\n", "    prompt = generationpy_slide_prompt_v2.format(\n", "        query=query,\n", "        existing_slides=existing_slides_context,\n", "        slide_content=content\n", "    )\n", "\n", "    # C. Call the generation function\n", "    # NOTE: We pass `all_slides_data` which is the correct list of dictionaries\n", "    new_slide_html = generate_general_slide(\n", "        query=prompt,\n", "        slide_content=content,\n", "        existing_slide_content=all_slides_data\n", "    )\n", "\n", "    # D. Store the new slide's data\n", "    all_slides_data.append({'name': slide_name, 'html': new_slide_html})\n", "\n", "    # <PERSON><PERSON> Display the result immediately\n", "    print(f\"✅ Successfully generated slide {slide_number}. Displaying below:\")\n", "    display(HTML(new_slide_html))\n", "    print(\"\\\\n\" + \"=\"*50 + \"\\\\n\")\n", "\n", "print(\"🎉🎉🎉 All slides have been generated successfully! 🎉🎉🎉\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "Elm1eKuEgtZ-", "outputId": "28013e0c-ccab-44a0-9b1b-f65e6fc61d45"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<!DOCTYPE html>\\n<html>\\n<head>\\n<title>EV Market Research - The Opportunity</title>\\n<style>\\n    @import url(\\'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap\\');\\n\\n    body, html {\\n        margin: 0;\\n        padding: 0;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        height: 100vh;\\n        background-color: #f0f0f0;\\n        font-family: \\'Roboto\\', sans-serif;\\n    }\\n\\n    .slide-container {\\n        width: 1280px; /* 720p resolution width */\\n        height: 720px;\\n        background-color: #1A1A1A; /* Dark background for a tech feel */\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        align-items: center;\\n        position: relative;\\n        overflow: hidden;\\n        box-shadow: 0 10px 20px rgba(0,0,0,0.2);\\n    }\\n\\n    .background-image {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background-image: url(\\'https://img.freepik.com/premium-photo/electric-car-charging-sunset-with-wind-turbines-background_1203353-45441.jpg\\');\\n        background-size: cover;\\n        background-position: center;\\n        filter: brightness(0.4); /* Darken the image to make text pop */\\n        z-index: 1;\\n    }\\n\\n    .slide-content {\\n        position: relative;\\n        z-index: 2;\\n        color: #FFFFFF;\\n        flex-grow: 1;\\n        overflow: hidden;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: flex-start; /* Align content to the top */\\n        align-items: flex-start; /* Align content to the left */\\n        text-align: left;\\n        width: 100%;\\n        padding: 60px 80px;\\n        box-sizing: border-box;\\n    }\\n\\n    .slide-content h3 {\\n        font-size: 2.5em; /* 40px */\\n        font-weight: 700;\\n        margin-bottom: 20px;\\n        line-height: 1.2;\\n    }\\n\\n    .main-layout {\\n        display: flex;\\n        width: 100%;\\n        height: calc(100% - 100px); /* Adjust height based on title area */\\n        align-items: flex-start;\\n        margin-top: 20px;\\n    }\\n\\n    .left-column {\\n        flex: 1;\\n        padding-right: 40px;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: space-between;\\n        height: 100%;\\n    }\\n\\n    .left-column p {\\n        font-size: 1.5em; /* 24px */\\n        font-weight: 300;\\n        line-height: 1.6;\\n        max-width: 95%;\\n    }\\n    \\n    .risk-section {\\n        margin-top: auto; /* Pushes this to the bottom of the available space */\\n        padding-top: 40px;\\n    }\\n\\n    .risk-section p {\\n        font-size: 1.25em; /* 20px */\\n        font-weight: 400;\\n        border-left: 4px solid #00AEEF;\\n        padding-left: 20px;\\n        max-width: 95%;\\n    }\\n\\n    .right-column {\\n        flex: 1;\\n        display: grid;\\n        grid-template-columns: repeat(2, 1fr);\\n        grid-template-rows: repeat(2, 1fr);\\n        gap: 40px;\\n        padding-left: 40px;\\n        height: 100%;\\n    }\\n\\n    .icon-card {\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        justify-content: center;\\n        text-align: center;\\n        background-color: rgba(255, 255, 255, 0.05);\\n        border-radius: 12px;\\n        padding: 20px;\\n        box-sizing: border-box;\\n    }\\n\\n    .icon-container {\\n        width: 80px;\\n        height: 80px;\\n        margin-bottom: 20px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n    }\\n\\n    .icon-container img {\\n        max-width: 100%;\\n        max-height: 100%;\\n        filter: invert(1) drop-shadow(0 0 5px rgba(0, 174, 239, 0.7));\\n    }\\n\\n    .icon-card p {\\n        font-size: 1.1em; /* 17.6px */\\n        font-weight: 400;\\n        line-height: 1.4;\\n        margin: 0;\\n    }\\n\\n</style>\\n</head>\\n<body>\\n\\n    <div class=\"slide-container\">\\n        <div class=\"background-image\"></div>\\n        <div class=\"slide-content\">\\n            <h3>The Opportunity: Navigating a Complex Market</h3>\\n            <div class=\"main-layout\">\\n                <div class=\"left-column\">\\n                    <div>\\n                        <p><strong>The Challenge:</strong> The EV market\\'s exponential expansion creates immense opportunities but also significant challenges for stakeholders struggling to keep pace.</p>\\n                    </div>\\n                    <div class=\"risk-section\">\\n                        <p><strong>The Risk of Inaction:</strong> Without a clear, data-driven understanding of these dynamics, businesses risk making uninformed decisions, leading to missed opportunities and wasted investments.</p>\\n                    </div>\\n                </div>\\n                <div class=\"right-column\">\\n                    <div class=\"icon-card\">\\n                        <div class=\"icon-container\">\\n                            <img src=\"https://www.svgrepo.com/show/429698/message-question-question-mark.svg\" alt=\"Consumer Preferences Icon\">\\n                        </div>\\n                        <p>Rapidly Shifting Consumer Preferences</p>\\n                    </div>\\n                    <div class=\"icon-card\">\\n                        <div class=\"icon-container\">\\n                            <img src=\"https://www.svgrepo.com/show/341455/power-energy-bolt-thunderbolt-electricity.svg\" alt=\"Technological Disruptions Icon\">\\n                        </div>\\n                        <p>Technological Disruptions</p>\\n                    </div>\\n                    <div class=\"icon-card\">\\n                        <div class=\"icon-container\">\\n                            <img src=\"https://www.svgrepo.com/show/499715/battery-car.svg\" alt=\"Supply Chains Icon\">\\n                        </div>\\n                        <p>Complex Supply Chains</p>\\n                    </div>\\n                    <div class=\"icon-card\">\\n                        <div class=\"icon-container\">\\n                            <img src=\"https://www.svgrepo.com/show/408372/location-pin-map.svg\" alt=\"Charging Infrastructure Icon\">\\n                        </div>\\n                        <p>Fragmented Charging Infrastructure</p>\\n                    </div>\\n                </div>\\n            </div>\\n        </div>\\n    </div>\\n\\n</body>\\n</html>'"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["all_slides_data[2][\"html\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "w-sfFJIwh7AN"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "pptx-planner-eMIzI0lx-py3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"02251f43a40b42c2a571307f04b80537": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "076b659fc5004335baeb07eff2eb76ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6372c2b8852449018630b88d06e391fe", "max": 612, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dd8982d45bdd461a8305c339c0b01f42", "value": 612}}, "0a8de638e0024f51bbc8eb4b941dd5ea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b35f73723f84f64ae268b3b2f4c4c99": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "10d380f565e54e48ba26c6f841b3ec95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "15bf646409de468ab2a26a67de26eeb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "15db0f4d98c84ecb97d9aec8c7a3994b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce0e3ba63ef8490b9c4109bc79c67090", "placeholder": "​", "style": "IPY_MODEL_39f8900b4ad3492a83edc2548bab47f8", "value": "modules.json: 100%"}}, "1a609dce99b24395afcaddc5c4309140": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a6bd4800ec44e18831d57ee3ce8d7da": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1be26a1c0f3f467b9296851019a40ab2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_46f7428d02cc450cb50cf98732fa2a53", "placeholder": "​", "style": "IPY_MODEL_0b35f73723f84f64ae268b3b2f4c4c99", "value": " 349/349 [00:00&lt;00:00, 25.7kB/s]"}}, "1e65157e24764abb9a0512749302bdf5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "274a168cef8849749fdf1599c07408ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "293440b5510a4268989e3fcea144ff66": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "29587a7bc49f4acb9104581ba29ba3dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2e090cad26374b1e9a916bb88a8f65f6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3566ab3b9fca473aba362ec4a6af86e9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "36dbbb8ffbb64ef8977433e45074d349": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c07bdf5d4fc3477a8ffe0ec7ef4904ff", "IPY_MODEL_5983152bac5f4cfca6ab389a445ecd09", "IPY_MODEL_7eae65b2a6ba4209aa62cc9454f67d9e"], "layout": "IPY_MODEL_3d1b67706bc5473b9cd58c1c622e95b6"}}, "377c8ccc3b3d4b7cb6e88635f1b5fc61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "39f8900b4ad3492a83edc2548bab47f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3a2e46e317f24a99b6ee677ed463427e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3ca38029c662487f977ea8a9047ec228": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b30e77e0a32441fe96b82f5ee148cbff", "IPY_MODEL_4527da52da254bc2aa6982807d50df4a", "IPY_MODEL_ccf8a74bd7e44c68a645988ff1ce9230"], "layout": "IPY_MODEL_e8f82f55b47f4237a63f5d89f9c51ede"}}, "3d1b67706bc5473b9cd58c1c622e95b6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d51df6215274aab8704a15e6d19a2a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3f26d85cfb59436c856d8470097a7478": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4197aa055e2c41b3ae7feb969aed77ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6e7b86435e984372afd1bb12baffd1c6", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_377c8ccc3b3d4b7cb6e88635f1b5fc61", "value": 1}}, "420d5fd4236d4ddfbb48d42bf5365ef1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4236a96d7dae49bf832abcca74fe8ee4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4511101fb1944733a693a10c5e637cda": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_57e960f82aa644439236d16ded565216", "placeholder": "​", "style": "IPY_MODEL_62e2d49108c04cda9cc665d0ddc50f20", "value": " 90.9M/90.9M [00:01&lt;00:00, 108MB/s]"}}, "4527da52da254bc2aa6982807d50df4a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f49b11881e1f4ec68026cd821d1c6ae1", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_10d380f565e54e48ba26c6f841b3ec95", "value": 53}}, "46f7428d02cc450cb50cf98732fa2a53": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4775cbc48327419bad6676d2b656ea91": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b763de7fae4b43158ecc2f5789ecca05", "IPY_MODEL_076b659fc5004335baeb07eff2eb76ea", "IPY_MODEL_48d0578e192940599b700d5e8c4d531c"], "layout": "IPY_MODEL_274a168cef8849749fdf1599c07408ed"}}, "48d0578e192940599b700d5e8c4d531c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d7a889100e584cbf8990ade7352a3c88", "placeholder": "​", "style": "IPY_MODEL_def9673616974430b8f76e657a37101b", "value": " 612/612 [00:00&lt;00:00, 44.7kB/s]"}}, "491c9d750db148d7a8a9a4bdf60c096d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4c21f26065fa4b62a5b625e51f8ab31b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d0aacf7d9f744ecaf347d8a563b70ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f45079644bf04efd915cde5de832aabf", "placeholder": "​", "style": "IPY_MODEL_29587a7bc49f4acb9104581ba29ba3dc", "value": "config_sentence_transformers.json: 100%"}}, "4d223b4565e74e8292c974f7944b7562": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4ef240c9da6f45b68045783963b5ee0d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "500e9587ada34f6888079343f722b030": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "57e960f82aa644439236d16ded565216": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "580cafd4252c4a379ffd8f542c7526bf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "58b9614c5b524e49bf83d4eebb1b3b1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9e6911bc9d424c36a6010ba25ebff9fb", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fc517e4aa0d942d287d868c7278faf8e", "value": 1}}, "5983152bac5f4cfca6ab389a445ecd09": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dfcf6c00a8e3434c8666a7e4b0924040", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c5852a97f8954cbab1bdb0d173a09299", "value": 190}}, "5bd29b7b3f8541468c18f3f559f68d3b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d1f00e3148b4d0f8831fd3a0c16a822": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a91006eccdf84c0db1486483f996d2e0", "IPY_MODEL_90d1f22276a242a3ab8c3d70ed87cb3c", "IPY_MODEL_a4cc184287cb405bbde40cdb94166d6d"], "layout": "IPY_MODEL_c5026f63cb314124a4754e1ed0a31247"}}, "5e251b0e5cae4496bd149a63385130ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5fb3b6332cef401f82371daedac5475a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61a1fc9f72d949c78b015dbef850098e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "62e2d49108c04cda9cc665d0ddc50f20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6372c2b8852449018630b88d06e391fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "638f0831256e4614b5b202a61371da19": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6a47ad6948b5480da382af420d5326cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b0f798ec708443996e7928dfe3beaf0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b12db388b664380b0b2061b6f96001b", "placeholder": "​", "style": "IPY_MODEL_c4dcffaefa8744858d9ad6a9e60bba93", "value": "special_tokens_map.json: 100%"}}, "6b12db388b664380b0b2061b6f96001b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6beb00dcfeb54c979bac27b541549216": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6d869ef355a7450482bf4967e5fab7f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6db0d668f95947d4a4a0f32dca081dfb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5bd29b7b3f8541468c18f3f559f68d3b", "placeholder": "​", "style": "IPY_MODEL_3a2e46e317f24a99b6ee677ed463427e", "value": "model.safetensors: 100%"}}, "6e7b86435e984372afd1bb12baffd1c6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "6fad0a4aa3664078bdae799ddd14a752": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "73d700ae686c4c4295b258caed55b8d6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6db0d668f95947d4a4a0f32dca081dfb", "IPY_MODEL_9c81288844a84920af8c44b340f5249a", "IPY_MODEL_4511101fb1944733a693a10c5e637cda"], "layout": "IPY_MODEL_fcdf4dcf21c649d4871de652a943e815"}}, "7b26d480a6ee4621b94950fa4a72021e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f36183a5c7248748b36b3b427375f2e", "placeholder": "​", "style": "IPY_MODEL_6fad0a4aa3664078bdae799ddd14a752", "value": "tokenizer_config.json: 100%"}}, "7dad2166383346fc845f0195d02a5d06": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fbe7037ec97041a1a41f12641ae2a8cb", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_edb8b1f7532244ffbc0e1f37be758d0c", "value": 116}}, "7df1bb4e163942529e8e3084ee194e42": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7eae65b2a6ba4209aa62cc9454f67d9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_02251f43a40b42c2a571307f04b80537", "placeholder": "​", "style": "IPY_MODEL_491c9d750db148d7a8a9a4bdf60c096d", "value": " 190/190 [00:00&lt;00:00, 9.26kB/s]"}}, "7f36183a5c7248748b36b3b427375f2e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "82e3d218904741f88d8205ee7ba9e8e7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b273022f6d74895aa2828121f109ac3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ab50f2d3002e43329e1ef86c8f1ed595", "placeholder": "​", "style": "IPY_MODEL_15bf646409de468ab2a26a67de26eeb6", "value": " 112/112 [00:00&lt;00:00, 9.15kB/s]"}}, "90d1f22276a242a3ab8c3d70ed87cb3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a1ddab702b4b4a0a8e6223331efa6f05", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6d869ef355a7450482bf4967e5fab7f0", "value": 1}}, "965f407882b741b390c6ab609192d4a1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c81288844a84920af8c44b340f5249a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_db537784b6214292a33080f2a10aacab", "max": 90868376, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_abd2d6781f4a41e0844814b4cabbbcf6", "value": 90868376}}, "9e6911bc9d424c36a6010ba25ebff9fb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "9ea5db05d8de43b8a22aade10d530c91": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a1ddab702b4b4a0a8e6223331efa6f05": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "a2692dcd074d44b2b572473919966dee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a8de638e0024f51bbc8eb4b941dd5ea", "max": 350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1e65157e24764abb9a0512749302bdf5", "value": 350}}, "a2bec94803bf4e2f82fd07ad543fef9f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a41640def05a4ec0a446c496445aa19c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6b0f798ec708443996e7928dfe3beaf0", "IPY_MODEL_f7b9f802fde146da8e3399332e9c67fc", "IPY_MODEL_8b273022f6d74895aa2828121f109ac3"], "layout": "IPY_MODEL_4c21f26065fa4b62a5b625e51f8ab31b"}}, "a4cc184287cb405bbde40cdb94166d6d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_580cafd4252c4a379ffd8f542c7526bf", "placeholder": "​", "style": "IPY_MODEL_7df1bb4e163942529e8e3084ee194e42", "value": " 10.5k/? [00:00&lt;00:00, 610kB/s]"}}, "a91006eccdf84c0db1486483f996d2e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4236a96d7dae49bf832abcca74fe8ee4", "placeholder": "​", "style": "IPY_MODEL_4d223b4565e74e8292c974f7944b7562", "value": "README.md: "}}, "aa27ec9709ba41bda729d663d6059fa7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ab50f2d3002e43329e1ef86c8f1ed595": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "abd2d6781f4a41e0844814b4cabbbcf6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b00a35d1a5d14bdda6f33c2388bc5250": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b30e77e0a32441fe96b82f5ee148cbff": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a47ad6948b5480da382af420d5326cf", "placeholder": "​", "style": "IPY_MODEL_d9df8b9c6d5c446c8258c7c696313057", "value": "sentence_bert_config.json: 100%"}}, "b763de7fae4b43158ecc2f5789ecca05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f26d85cfb59436c856d8470097a7478", "placeholder": "​", "style": "IPY_MODEL_293440b5510a4268989e3fcea144ff66", "value": "config.json: 100%"}}, "b917085185c64f939537b9af35372433": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa27ec9709ba41bda729d663d6059fa7", "placeholder": "​", "style": "IPY_MODEL_d2b764726b8640e18daff532208b61e4", "value": "tokenizer.json: "}}, "b959242e06a445749cf02f234a36112c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b26d480a6ee4621b94950fa4a72021e", "IPY_MODEL_a2692dcd074d44b2b572473919966dee", "IPY_MODEL_cfb56f8b8dfb4235be6f0707e40c6e63"], "layout": "IPY_MODEL_5e251b0e5cae4496bd149a63385130ed"}}, "baffaa8fd0f44f478eeef941ae2e01fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd07daf10228412b85e112c54b339628": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c07bdf5d4fc3477a8ffe0ec7ef4904ff": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_baffaa8fd0f44f478eeef941ae2e01fe", "placeholder": "​", "style": "IPY_MODEL_638f0831256e4614b5b202a61371da19", "value": "config.json: 100%"}}, "c0b2c7fc0c7f4a8f838d9f352375487b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c1456ed60c6a4938a1ef7592b083aea6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4d0aacf7d9f744ecaf347d8a563b70ae", "IPY_MODEL_7dad2166383346fc845f0195d02a5d06", "IPY_MODEL_fe9fc50dc8dd4043a9a291f060312e53"], "layout": "IPY_MODEL_965f407882b741b390c6ab609192d4a1"}}, "c4dcffaefa8744858d9ad6a9e60bba93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c5026f63cb314124a4754e1ed0a31247": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c5852a97f8954cbab1bdb0d173a09299": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ccc92a745d38442ca36c61aa50d7dc6c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ccf8a74bd7e44c68a645988ff1ce9230": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a6bd4800ec44e18831d57ee3ce8d7da", "placeholder": "​", "style": "IPY_MODEL_3d51df6215274aab8704a15e6d19a2a3", "value": " 53.0/53.0 [00:00&lt;00:00, 4.81kB/s]"}}, "cdc5c17124d24b44a36d60c6edd30ac6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_82e3d218904741f88d8205ee7ba9e8e7", "placeholder": "​", "style": "IPY_MODEL_c0b2c7fc0c7f4a8f838d9f352375487b", "value": " 232k/? [00:00&lt;00:00, 11.1MB/s]"}}, "ce0e3ba63ef8490b9c4109bc79c67090": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cfb56f8b8dfb4235be6f0707e40c6e63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3566ab3b9fca473aba362ec4a6af86e9", "placeholder": "​", "style": "IPY_MODEL_2e090cad26374b1e9a916bb88a8f65f6", "value": " 350/350 [00:00&lt;00:00, 32.3kB/s]"}}, "d2b764726b8640e18daff532208b61e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d7a889100e584cbf8990ade7352a3c88": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9df8b9c6d5c446c8258c7c696313057": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "db38be6bf05344218d45bdb8c2e0bb1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bd07daf10228412b85e112c54b339628", "placeholder": "​", "style": "IPY_MODEL_500e9587ada34f6888079343f722b030", "value": "vocab.txt: "}}, "db537784b6214292a33080f2a10aacab": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dd8982d45bdd461a8305c339c0b01f42": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "def9673616974430b8f76e657a37101b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dfcf6c00a8e3434c8666a7e4b0924040": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e79a8152715442678d55dd8018187fdd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e86dddafaaa248188a0f48139d65b37b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b917085185c64f939537b9af35372433", "IPY_MODEL_58b9614c5b524e49bf83d4eebb1b3b1d", "IPY_MODEL_e8c3ba8b4dfa4f9487587be677261adb"], "layout": "IPY_MODEL_420d5fd4236d4ddfbb48d42bf5365ef1"}}, "e8c3ba8b4dfa4f9487587be677261adb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a609dce99b24395afcaddc5c4309140", "placeholder": "​", "style": "IPY_MODEL_a2bec94803bf4e2f82fd07ad543fef9f", "value": " 466k/? [00:00&lt;00:00, 19.5MB/s]"}}, "e8f82f55b47f4237a63f5d89f9c51ede": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ea93e3517a7d40b4a1f3403c93e93fc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_15db0f4d98c84ecb97d9aec8c7a3994b", "IPY_MODEL_fd57034d9088484cb35a240fbdd0042b", "IPY_MODEL_1be26a1c0f3f467b9296851019a40ab2"], "layout": "IPY_MODEL_b00a35d1a5d14bdda6f33c2388bc5250"}}, "edb8b1f7532244ffbc0e1f37be758d0c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "eff0d5bdf471457685a77b1161206e20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_db38be6bf05344218d45bdb8c2e0bb1c", "IPY_MODEL_4197aa055e2c41b3ae7feb969aed77ec", "IPY_MODEL_cdc5c17124d24b44a36d60c6edd30ac6"], "layout": "IPY_MODEL_e79a8152715442678d55dd8018187fdd"}}, "f45079644bf04efd915cde5de832aabf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f49b11881e1f4ec68026cd821d1c6ae1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7b9f802fde146da8e3399332e9c67fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9ea5db05d8de43b8a22aade10d530c91", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4ef240c9da6f45b68045783963b5ee0d", "value": 112}}, "fbe7037ec97041a1a41f12641ae2a8cb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc517e4aa0d942d287d868c7278faf8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fcdf4dcf21c649d4871de652a943e815": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd57034d9088484cb35a240fbdd0042b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5fb3b6332cef401f82371daedac5475a", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_61a1fc9f72d949c78b015dbef850098e", "value": 349}}, "fe9fc50dc8dd4043a9a291f060312e53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ccc92a745d38442ca36c61aa50d7dc6c", "placeholder": "​", "style": "IPY_MODEL_6beb00dcfeb54c979bac27b541549216", "value": " 116/116 [00:00&lt;00:00, 7.76kB/s]"}}}}}, "nbformat": 4, "nbformat_minor": 0}