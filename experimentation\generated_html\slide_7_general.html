<!--
Generated by: HTML-to-PptxGenJS Experimentation Notebook
Timestamp: 2025-08-10 15:40:24
Query: Make me a presentation about building a system which takes an internal company database of contract ...
Slide: slide_7 (general)
Input Tokens: 1101
Output Tokens: 676
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - AI Contract System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5; /* Light gray background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .slide-container {
            width: 1280px; /* 720p width */
            height: 720px; /* 720p height */
            background-color: #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            box-sizing: border-box;
            position: relative;
        }
        .gradient-text {
            background: linear-gradient(90deg, #4A90E2, #50E3C2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }
        .ai-illustration {
            position: absolute;
            bottom: 40px; /* Adjust as needed */
            right: 40px; /* Adjust as needed */
            width: 250px; /* Adjust size as needed */
            height: auto;
            opacity: 0.8;
        }
    


/* AUTO-FIT CSS TEMPLATE - Ensures content fits within 720px height */
body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
    position: relative;
    background-color: #FFFFFF;
    font-family: 'Roboto', sans-serif;
    overflow: hidden;
    color: #272774;
    display: flex;
    flex-direction: column;
}

/* Main content container with auto-fit */
.slide-content {
    position: absolute;
    top: 60px;
    left: 60px;
    right: 60px;
    bottom: 80px; /* Leave space for footer */
    max-width: 1160px;
    max-height: 580px; /* 720 - 60 (top) - 80 (bottom) */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1;
}

/* Auto-scaling title */
.slide-title {
    font-size: clamp(1.8em, 4vw, 2.3em); /* Responsive title size */
    font-weight: 700;
    margin-bottom: clamp(15px, 3vh, 30px);
    color: #272774;
    flex-shrink: 0; /* Don't shrink title */
    line-height: 1.2;
}

/* Content area that auto-fits remaining space */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* Allow flex shrinking */
}

/* Split content layout */
.split-content {
    display: flex;
    gap: clamp(20px, 3vw, 40px);
    flex: 1;
    min-height: 0;
}

/* Sections that auto-scale */
.visual-section, .bullet-points-section {
    flex: 1;
    background: #F5E2DA;
    border-radius: 8px;
    padding: clamp(10px, 2vh, 20px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

/* Auto-scaling text elements - Improved readability */
.content-text {
    font-size: clamp(1.0em, 1.5vw, 1.2em);  /* Increased min from 0.8em to 1.0em */
    line-height: 1.4;
    margin-bottom: clamp(8px, 1.5vh, 15px);
}

/* Bullet points with auto-scaling - Better readability */
ul {
    margin: 0;
    padding-left: 20px;
    flex: 1;
    overflow: hidden;
}

li {
    font-size: clamp(0.9em, 1.4vw, 1.1em);  /* Increased min from 0.75em to 0.9em */
    line-height: 1.5;
    margin-bottom: clamp(5px, 1vh, 10px);
    display: flex;
    align-items: flex-start;
}

/* Icon sizing */
.icon {
    margin-right: 10px;
    flex-shrink: 0;
    font-size: clamp(0.8em, 1.2vw, 1em);
    padding-top: 2px;
}

/* Image placeholders that scale */
.image-placeholder {
    position: relative;
    width: 100%;
    height: clamp(120px, 25vh, 200px);
    background: #FFFFFF;
    border: 2px solid #272774;
    border-radius: 8px;
    margin-bottom: clamp(10px, 2vh, 20px);
    flex-shrink: 0;
}

/* Progress bars and other elements */
.progress-bar-container {
    margin-top: auto;
    padding-top: clamp(5px, 1vh, 10px);
}

/* Fixed elements (logo, page number) */
.page-number {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #EBC4B4;
    color: #272774;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    font-size: clamp(12px, 1.2vw, 14px);
    z-index: 10;
}

.logo {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

.logo img {
    height: clamp(40px, 6vh, 60px);
}

/* Responsive adjustments for content-heavy slides */
@media (max-height: 720px) {
    .slide-title {
        font-size: 1.8em;
        margin-bottom: 15px;
    }
    
    .content-text, li {
        font-size: 0.9em;  /* Increased from 0.8em */
        line-height: 1.3;
        margin-bottom: 8px;
    }
    
    .visual-section, .bullet-points-section {
        padding: 15px;
    }
    
    .image-placeholder {
        height: 120px;
        margin-bottom: 10px;
    }
}

/* Utility classes for different content densities - Improved readability */
.content-light .slide-title { font-size: 2.5em; }
.content-light .content-text { font-size: 1.1em; }
.content-light li { font-size: 1em; }

.content-medium .slide-title { font-size: 2.2em; }
.content-medium .content-text { font-size: 1.0em; }  /* Increased from 0.95em */
.content-medium li { font-size: 0.9em; }  /* Increased from 0.85em */

.content-heavy .slide-title { font-size: 2.1em; margin-bottom: 15px; }  /* Increased from 1.9em */
.content-heavy .content-text { font-size: 0.9em; line-height: 1.3; }  /* Increased from 0.8em */
.content-heavy li { font-size: 0.85em; line-height: 1.4; margin-bottom: 6px; }  /* Increased from 0.75em */
.content-heavy .visual-section, .content-heavy .bullet-points-section { padding: 12px; }
.content-heavy .image-placeholder { height: 100px; margin-bottom: 8px; }
</style>
</head>
<body class="content-light">
    <div class="slide-container">
        <div class="text-center mb-16">
            <h2 class="text-6xl font-bold text-gray-800 mb-6">Thank you for your attention</h2>
            <h3 class="text-5xl font-semibold gradient-text">Questions?</h3>
        </div>

        <img src="https://c8.alamy.com/comp/2A24JA6/abstract-artificial-intelligence-concept-vector-illustration-2A24JA6.jpg" alt="Abstract AI Concept Illustration" class="ai-illustration">
    </div>
</body>
</html>