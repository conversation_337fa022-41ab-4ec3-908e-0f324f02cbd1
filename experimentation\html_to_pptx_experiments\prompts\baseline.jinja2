You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code.

STEP 1: ANALYZE THE HTML FIRST
Before generating code, carefully examine the HTML for:
- Background colors (from CSS styles, body, containers)
- Text colors (from CSS classes, inline styles, color properties)
- Font sizes and hierarchy (h1, h2, h3, p tags and their CSS)
- Layout structure (grid, flexbox, columns, sections)
- Content emphasis (bold, italic, color variations)

STEP 2: EXTRACT DYNAMIC VALUES
- Background: Look for background-color, background, or theme colors
- Text colors: Extract from color properties, CSS classes
- Font sizes: Map HTML hierarchy → h1=32-40px, h2=20-28px, h3=16-20px, p=12-16px
- Layout: Detect grid patterns, column structures, content sections

CSS-TO-PPTXGENJS MAPPING GUIDE:
- background-color: #0D1B2A → slide.background = { color: '0D1B2A' }
- color: #E0E0E0 → color: 'E0E0E0'
- font-weight: bold → bold: true
- font-style: italic → italic: true
- text-align: center → align: 'center'
- Remove # from hex colors: #FF6B6B → 'FF6B6B'

COLOR FORMATTING RULES - CRITICAL:
- Colors must be simple quoted strings: 'FFFFFF', '2C3E50', 'E0E0E0'
- NO extra quotes or backslashes: 'FFFFFF\' is WRONG
- NO hash symbols: '#FFFFFF' is WRONG
- NO variables: BG_COLOR is WRONG
- NO backslashes anywhere: \' is FORBIDDEN
- CORRECT format: slide.background = { color: 'FFFFFF' };

STRING FORMATTING RULES - CRITICAL:
- ALL text must be quoted: slide.addText('Title Text', { ... })
- ALL colors must be quoted: color: 'FFFFFF', color: '2C3E50'
- ALL text in arrays must be quoted: { text: '* Bullet point', ... }
- NO unquoted text: addText(Title, { is WRONG
- NO unquoted colors: color: FFFFFF is WRONG
- For apostrophes in text, escape them: 'we\'ll' not 'we'll'
- CORRECT: slide.addText('Don\'t use backslashes', { color: '2C3E50' });

FALLBACK VALUES (use if HTML analysis unclear):
- Default background: '0D1B2A' (dark blue)
- Default text color: 'E0E0E0' (light gray)
- Default accent color: '88CCEE' (light blue)
- Default title size: 32, subtitle: 20, content: 14

STEP 3: GENERATE PPTXGENJS CODE
CRITICAL RULES - FOLLOW EXACTLY:
1. Generate a COMPLETE function called createPresentation()
2. ALWAYS start with: const pptx = new PptxGenJS();
3. ALWAYS end with: return pptx.writeFile({ fileName: 'presentation.pptx' });
4. Use ONLY simple addText() calls - NO complex nested objects
5. Colors MUST be literal strings extracted from HTML: color: 'E0E0E0' (NOT variables)

POSITIONING RULES - CRITICAL FOR PROPER LAYOUT:
6. SLIDE DIMENSIONS: Standard slide is 10 inches wide × 5.625 inches tall
7. SAFE MARGINS: Keep content between x: 0.5-9.5 and y: 0.5-5.0
8. TITLE POSITIONING: Main titles at y: 0.5-1.0, subtitles at y: 1.2-1.8
9. CONTENT SPACING: Leave 0.3-0.5 inches between text blocks vertically
10. GRID LAYOUTS: For 2-column layouts use x: 0.5,5.25 with w: 4.0 each
11. TEXT WIDTH: Always specify w: (width) to prevent text overflow
12. VERTICAL FLOW: Start content at y: 2.0, increment by 0.8-1.0 per section

HTML to convert:
{{html_content}}

Slide name: {{slide_name}}

FORBIDDEN PATTERNS - DO NOT GENERATE:
❌ 'FFFFFF\' (backslash before closing quote)
❌ \'FFFFFF' (backslash before opening quote)
❌ color: \'red' (backslash in color values)
❌ text: \'content\' (backslashes around text)
❌ 'we'll' (unescaped apostrophes - use 'we\'ll')
❌ '* 'Popcorning': text' (nested single quotes)
❌ 'text with 'quotes' inside' (quotes inside quotes)

✅ CORRECT PATTERNS:
✅ 'FFFFFF' (simple quoted strings)
✅ color: 'red' (clean color values)
✅ text: 'content' (clean text strings)
✅ 'we\'ll' (properly escaped apostrophes)
✅ '* Popcorning: text' (no nested quotes)
✅ 'text with quotes inside' (remove inner quotes)

Generate ONLY the JavaScript function. Keep it simple and syntactically correct.
