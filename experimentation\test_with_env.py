#!/usr/bin/env python3
"""
Test script with proper environment variable loading
"""

import sys
import os
import json
from pathlib import Path

# Load environment variables FIRST
from dotenv import load_dotenv

# Try multiple locations for .env file (including local.env)
env_paths = [
    "../local.env",      # Parent directory (local.env)
    "../.env",           # Parent directory (.env)
    "../../local.env",   # Two levels up (local.env)
    "../../.env",        # Two levels up (.env)
    "local.env",         # Current directory (local.env)
    ".env",              # Current directory (.env)
    "../../../local.env" # Three levels up (local.env)
]

env_loaded = False
for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"🔑 Environment variables loaded from: {env_path}")
        env_loaded = True
        break

if not env_loaded:
    print("⚠️  No .env file found. You may need to set environment variables manually.")
    print("Available .env locations checked:")
    for path in env_paths:
        abs_path = os.path.abspath(path)
        exists = "✅" if os.path.exists(path) else "❌"
        print(f"   {exists} {abs_path}")

# Check if required API keys are available
required_keys = ["GEMINI_API_KEY", "TAVILY_API_KEY"]
missing_keys = []

for key in required_keys:
    if not os.getenv(key):
        missing_keys.append(key)

if missing_keys:
    print(f"\n⚠️  Missing API keys: {', '.join(missing_keys)}")
    print("You can set them manually:")
    for key in missing_keys:
        print(f"   $env:{key} = 'your-api-key'")
    print("\nOr add them to your .env file:")
    for key in missing_keys:
        print(f"   {key}=your-api-key")
else:
    print("✅ All required API keys are available")

# Add parent directory to path
sys.path.append("../")
sys.path.append("html_to_pptx_experiments")

def test_environment_setup():
    """Test environment setup without importing LLM modules"""
    print("\n🧪 Testing Environment Setup")
    print("=" * 50)
    
    # Test validation functions (no API keys needed)
    print("1. Testing validation functions...")
    try:
        from validate_js_output import main as validate_js
        from evaluate_translation import main as evaluate_translation
        
        # Test with sample data
        test_js = """
        function createPresentation() {
            const pptx = new PptxGenJS();
            const slide = pptx.addSlide();
            slide.background = { color: '2C3E50' };
            slide.addText('Test Title', {
                x: 1.0, y: 0.8, w: 8.0, h: 1.0,
                fontSize: 32, color: 'FFFFFF', bold: true
            });
            return pptx.writeFile({ fileName: 'presentation.pptx' });
        }
        """
        
        test_html = """
        <!DOCTYPE html>
        <html>
        <head><style>body { background-color: #2C3E50; color: #FFFFFF; }</style></head>
        <body><h1>Test Title</h1></body>
        </html>
        """
        
        validation_result = validate_js(test_js)
        quality_result = evaluate_translation(test_js, test_html)
        
        print(f"   ✅ Validation successful:")
        print(f"      - Overall valid: {validation_result.get('overall_valid', False)}")
        print(f"      - Quality score: {quality_result.get('overall_quality_score', 0)}/100")
        
    except Exception as e:
        print(f"   ❌ Validation test failed: {e}")
        return False
    
    # Test LLM import (requires API keys)
    print("\n2. Testing LLM import...")
    if missing_keys:
        print(f"   ⚠️  Skipping LLM test - missing API keys: {', '.join(missing_keys)}")
    else:
        try:
            from llm.llmwrapper import LLM
            llm = LLM(provider="gemini", model="gemini-1.5-flash")
            print(f"   ✅ LLM initialized successfully: {llm.provider}")
        except Exception as e:
            print(f"   ❌ LLM initialization failed: {e}")
            print("   This might be OK if the LLM is working but has different attributes")
            # Don't return False here, continue with other tests
    
    # Test HTML-to-PptxGenJS translator import
    print("\n3. Testing translator import...")
    if missing_keys:
        print("   ⚠️  Skipping translator test - missing API keys")
    else:
        try:
            from pptx_generation.html_to_pptx_translator import llm_html_to_pptxgenjs_single
            print("   ✅ Translator imported successfully")
        except Exception as e:
            print(f"   ❌ Translator import failed: {e}")
            return False
    
    print("\n🎉 Environment test complete!")
    print("=" * 50)
    
    if missing_keys:
        print("⚠️  Some tests were skipped due to missing API keys.")
        print("To run full tests:")
        print("1. Add API keys to your .env file or set environment variables")
        print("2. Run this test again")
    else:
        print("✅ Environment is properly configured!")
        print("You can now run:")
        print("1. poetry run python test_prompt_flow.py")
        print("2. jupyter notebook prompt_flow_experimentation.ipynb")
    
    return True

if __name__ == "__main__":
    test_environment_setup()
