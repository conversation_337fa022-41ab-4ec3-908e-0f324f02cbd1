import sys
import os
import json
from pathlib import Path
import time

# Load environment variables FIRST (before importing LLM modules)
from dotenv import load_dotenv

# Try multiple locations for environment file (including local.env)
env_paths = ["../local.env", "../.env", "local.env", ".env"]
env_loaded = False

for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"🔑 Environment variables loaded from: {env_path}")
        env_loaded = True
        break

if not env_loaded:
    print("⚠️  No environment file found. You may need to set environment variables manually.")
    print("Available locations checked: ../local.env, ../.env, local.env, .env")

# Check for required API keys
required_keys = ["GEMINI_API_KEY", "TAVILY_API_KEY"]
missing_keys = [key for key in required_keys if not os.getenv(key)]

if missing_keys:
    print(f"⚠️  Missing API keys: {', '.join(missing_keys)}")
    print("You can set them manually in this notebook:")
    for key in missing_keys:
        print(f"os.environ['{key}'] = 'your-api-key'")
else:
    print("✅ All required API keys are available")

# Add parent directory to path
sys.path.append("../")

print("📦 Ready to import modules...")

# Test async support in Jupyter
import asyncio

async def test_async():
    await asyncio.sleep(0.1)
    return "✅ Async/await is working!"

# This should work in modern Jupyter environments
result = await test_async()
print(result)
print("🎉 Your Jupyter environment supports top-level await!")

# Uncomment and set your API keys if needed
# os.environ['GEMINI_API_KEY'] = 'your-gemini-api-key-here'
# os.environ['TAVILY_API_KEY'] = 'your-tavily-api-key-here'

# Verify API keys are set
api_keys_status = {
    'GEMINI_API_KEY': '✅ Set' if os.getenv('GEMINI_API_KEY') else '❌ Missing',
    'TAVILY_API_KEY': '✅ Set' if os.getenv('TAVILY_API_KEY') else '❌ Missing'
}

print("API Keys Status:")
for key, status in api_keys_status.items():
    print(f"   {key}: {status}")

# Import existing modules (following Duy's pattern)
try:
    from pptx_generation.planner import Planner
    from pptx_generation.generation import Generator
    from htmlrender.renderer import HTMLRenderer
    from llm.llmwrapper import LLM
    
    # Force reload the translator module to get the latest version
    import importlib
    import pptx_generation.html_to_pptx_translator as translator_module
    importlib.reload(translator_module)
    
    from pptx_generation.html_to_pptx_translator import (
        extract_text_from_html,
        extract_title_from_html,
        extract_content_from_html,
        llm_html_to_pptxgenjs_single,
        llm_html_to_pptxgenjs_combined
    )
    
    # Verify the function is actually async after reload
    import asyncio
    print(f"✅ All modules imported successfully")
    print(f"🔍 After reload - Is llm_html_to_pptxgenjs_single async: {asyncio.iscoroutinefunction(llm_html_to_pptxgenjs_single)}")
    
except Exception as e:
    print(f"❌ Module import failed: {e}")
    print("\nTroubleshooting:")
    print("1. Make sure API keys are set in local.env or .env file")
    print("2. Check if all dependencies are installed: poetry install")
    print("3. Try installing tavily: poetry add tavily-python")
    print("4. Restart the notebook kernel and try again")

# Initialize components (following Duy's pattern)
try:
    llm1 = LLM(provider="gemini", model="gemini-2.5-flash")
    llm2 = LLM(provider="gemini", model="gemini-2.5-flash")
    pln, gen = Planner(), Generator()
    html_renderer = HTMLRenderer()

    print("✅ LLM and pipeline components initialized")
    print(f"   - LLM1: {llm1.provider}")
    print(f"   - LLM2: {llm2.provider}")
    print(f"   - Planner, Generator, HTMLRenderer ready")
except Exception as e:
    print(f"❌ Component initialization failed: {e}")
    print("\nMake sure the previous cell ran successfully and all modules were imported.")

# Test query (following Duy's pattern)
query = "Make me a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document"

print(f"🎯 Test Query: {query}")

# Step 1: Generate outline (following app.py flow)
print("📋 Step 1: Generating presentation outline...")

# Generate outline first (like /generate-outline endpoint)
outline_result = await pln.outline(query=query, llm=llm1)
outline_text = outline_result['response']

print("✅ Outline generated!")
print(f"   - Outline length: {len(outline_text)} characters")
print(f"   - Preview: {outline_text[:200]}...")

# Step 2: Generate slide content from outline (following app.py flow)
print("📝 Step 2: Generating detailed slide content...")

# Generate slide content using outline (like /generate-slides endpoint)
slide_content_result = await pln.slide_content(
    query=query,
    outline_response=outline_text,
    llm=llm1
)

# Extract slide content
processed_slide_content = pln.extract_slide_content(
    slide_content_response=slide_content_result['response']
)

print("✅ Slide content generated!")
print(f"   - Generated {len(processed_slide_content['slide_content'])} slides")

# Show slide titles
for slide_key, slide_content in processed_slide_content['slide_content'].items():
    title = slide_content.split('\n')[0].replace('**', '').replace('*', '')
    print(f"   - {slide_key}: {title}")

# Step 3: Generate brainstorm for design/styling (following app.py WebSocket flow)
print("🎨 Step 3: Generating design brainstorm...")

# Generate brainstorm for styling (like WebSocket endpoint)
brainstorm_result = await pln.brainstorm(query=query, llm=llm1)
brainstorm_output = brainstorm_result['response']

print("✅ Design brainstorm generated!")
print(f"   - Brainstorm length: {len(brainstorm_output)} characters")
print(f"   - Preview: {brainstorm_output[:200]}...")

# Setup for HTML slide generation (following app.py flow)
print("🌐 Setting up HTML slide generation...")

# Helper function to ensure full HTML documents
def ensure_full_html(html_text: str) -> str:
    if not html_text:
        return html_text
    txt = html_text.strip()
    # If already a full document, return as-is
    if '<!DOCTYPE html>' in txt and '</html>' in txt:
        return txt
    # If it has <html> ... </html> but missing doctype, prepend it
    if '<html' in txt and '</html>' in txt:
        return '<!DOCTYPE html>\n' + txt
    # Otherwise, wrap the snippet in a minimal full document
    return (
        '<!DOCTYPE html><html><head><meta charset="utf-8"></head><body>'
        + txt +
        '</body></html>'
    )

# Get slide content
slide_content_dict = processed_slide_content['slide_content']

# Common parameters for all slides (like app.py)
common_params = {
    'query': query,
    'generator_llm': llm2,
    'reviewer_llm': llm2,
    'review': False,  # Set to True if you want review step
    'brainstorm_output': brainstorm_output
}

# Initialize html_slides dictionary
html_slides = {}

print(f"🔍 Setup complete:")
print(f"   - Query: {query[:100]}...")
print(f"   - Brainstorm output length: {len(brainstorm_output)}")
print(f"   - Available slides: {list(slide_content_dict.keys())}")
print(f"   - Ready to generate HTML slides!")

# Step 4a: Generate Title Slide (slide_1)
print("📄 Step 4a: Generating Title Slide...")

slide_key = 'slide_1'
slide_number = 1

if slide_key in slide_content_dict:
    slide_content = slide_content_dict[slide_key]
    print(f"🔍 Title slide info:")
    print(f"   - Slide content length: {len(slide_content)}")
    print(f"   - Slide content preview: {slide_content[:200]}...")
    
    try:
        print(f"Generating title slide...")
        
        # Generate title slide using official generator
        result = await gen.generate_title_slide(
            slide_content=slide_content,
            **common_params
        )
        
        # Ensure full HTML document
        html_text = result.get('html_code', '')
        html_code = ensure_full_html(html_text)
        
        result = {
            'html_code': html_code,
            'input_token_count': result.get('input_token_count', 0),
            'output_token_count': result.get('output_token_count', 0)
        }
        
        html_slides[slide_key] = result
        print(f"   ✅ Title slide generated successfully!")
        print(f"   - HTML length: {len(result.get('html_code', ''))}")
        
    except Exception as e:
        print(f"   ❌ Official generator failed for title slide: {e}")
        print(f"   - Error type: {type(e).__name__}")
        
        # Fallback: Direct LLM call with official prompt
        print(f"\n🔄 Fallback: Direct LLM call for title slide...")
        try:
            from prompt.official_prompts import generationpy_official_title_slide_prompt
            
            title_slide_prompt = generationpy_official_title_slide_prompt.format(
                query=query, 
                slide_content=slide_content,
                brain_storm=brainstorm_output
            )
            
            llm_response = await llm2.call(query=title_slide_prompt)
            llm_text = llm_response.get('text', '')
            html_code = ensure_full_html(llm_text)
            
            result = {
                'html_code': html_code,
                'input_token_count': llm_response.get('input_token_count', 0),
                'output_token_count': llm_response.get('output_token_count', 0)
            }
            
            html_slides[slide_key] = result
            print(f"   ✅ Fallback title slide generated successfully!")
            print(f"   - HTML length: {len(result.get('html_code', ''))}")
            
        except Exception as fallback_e:
            print(f"   ❌ Fallback also failed: {fallback_e}")
else:
    print(f"   ⚠️ No slide_1 found in slide content dictionary")

# Step 4b: Generate Agenda Slide (slide_2)
print("📋 Step 4b: Generating Agenda Slide...")

slide_key = 'slide_2'
slide_number = 2

if slide_key in slide_content_dict:
    slide_content = slide_content_dict[slide_key]
    print(f"🔍 Agenda slide info:")
    print(f"   - Slide content length: {len(slide_content)}")
    print(f"   - Slide content preview: {slide_content[:200]}...")
    
    try:
        print(f"Generating agenda slide...")
        
        # Generate agenda slide using official generator
        result = await gen.generate_agenda_slide(
            slide_content=slide_content,
            **common_params
        )
        
        # Ensure full HTML document
        html_text = result.get('html_code', '')
        html_code = ensure_full_html(html_text)
        
        result = {
            'html_code': html_code,
            'input_token_count': result.get('input_token_count', 0),
            'output_token_count': result.get('output_token_count', 0)
        }
        
        html_slides[slide_key] = result
        print(f"   ✅ Agenda slide generated successfully!")
        print(f"   - HTML length: {len(result.get('html_code', ''))}")
        
    except Exception as e:
        print(f"   ❌ Official generator failed for agenda slide: {e}")
        print(f"   - Error type: {type(e).__name__}")
        
        # Fallback: Direct LLM call with official prompt
        print(f"\n🔄 Fallback: Direct LLM call for agenda slide...")
        try:
            from prompt.official_prompts import generationpy_official_agenda_slide_prompt
            
            agenda_slide_prompt = generationpy_official_agenda_slide_prompt.format(
                query=query, 
                slide_content=slide_content,
                brain_storm=brainstorm_output
            )
            
            llm_response = await llm2.call(query=agenda_slide_prompt)
            llm_text = llm_response.get('text', '')
            html_code = ensure_full_html(llm_text)
            
            result = {
                'html_code': html_code,
                'input_token_count': llm_response.get('input_token_count', 0),
                'output_token_count': llm_response.get('output_token_count', 0)
            }
            
            html_slides[slide_key] = result
            print(f"   ✅ Fallback agenda slide generated successfully!")
            print(f"   - HTML length: {len(result.get('html_code', ''))}")
            
        except Exception as fallback_e:
            print(f"   ❌ Fallback also failed: {fallback_e}")
else:
    print(f"   ⚠️ No slide_2 found in slide content dictionary")

# Step 4c: Generate General Slides (slide_3, slide_4, etc.)
print("📊 Step 4c: Generating General Slides...")

# Find all general slides (slide_3 and above)
general_slides = {k: v for k, v in slide_content_dict.items() 
                 if k.startswith('slide_') and int(k.split('_')[1]) > 2}

if general_slides:
    print(f"Found {len(general_slides)} general slides: {list(general_slides.keys())}")
    
    for slide_key in sorted(general_slides.keys(), key=lambda k: int(k.split('_')[1])):
        slide_number = int(slide_key.split('_')[1])
        slide_content = general_slides[slide_key]
        
        print(f"\n🔍 Generating slide {slide_number}:")
        print(f"   - Slide content length: {len(slide_content)}")
        print(f"   - Slide content preview: {slide_content[:200]}...")
        
        try:
            print(f"Generating general slide {slide_number}...")
            
            # Generate general slide using official generator
            result = await gen.generate_general_slide(
                slide_content=slide_content,
                **common_params
            )
            
            # Ensure full HTML document
            html_text = result.get('html_code', '')
            html_code = ensure_full_html(html_text)
            
            result = {
                'html_code': html_code,
                'input_token_count': result.get('input_token_count', 0),
                'output_token_count': result.get('output_token_count', 0)
            }
            
            html_slides[slide_key] = result
            print(f"   ✅ General slide {slide_number} generated successfully!")
            print(f"   - HTML length: {len(result.get('html_code', ''))}")
            
        except Exception as e:
            print(f"   ❌ Official generator failed for slide {slide_number}: {e}")
            print(f"   - Error type: {type(e).__name__}")
            
            # Fallback: Direct LLM call with official prompt
            print(f"\n🔄 Fallback: Direct LLM call for slide {slide_number}...")
            try:
                from prompt.official_prompts import generationpy_official_general_slide_prompt
                
                general_slide_prompt = generationpy_official_general_slide_prompt.format(
                    query=query, 
                    slide_content=slide_content,
                    brain_storm=brainstorm_output
                )
                
                llm_response = await llm2.call(query=general_slide_prompt)
                llm_text = llm_response.get('text', '')
                html_code = ensure_full_html(llm_text)
                
                result = {
                    'html_code': html_code,
                    'input_token_count': llm_response.get('input_token_count', 0),
                    'output_token_count': llm_response.get('output_token_count', 0)
                }
                
                html_slides[slide_key] = result
                print(f"   ✅ Fallback general slide {slide_number} generated successfully!")
                print(f"   - HTML length: {len(result.get('html_code', ''))}")
                
            except Exception as fallback_e:
                print(f"   ❌ Fallback also failed for slide {slide_number}: {fallback_e}")
else:
    print(f"   ℹ️ No general slides found (only title and agenda slides)")

print(f"\n🎉 HTML slide generation complete!")
print(f"   - Total slides generated: {len(html_slides)}")
print(f"   - Generated slides: {list(html_slides.keys())}")

# Enhancement 1: HTML File Persistence
print("💾 Enhancement 1: Saving HTML slides to files...")

import os
from datetime import datetime
from pathlib import Path

# Create output directory
html_output_dir = Path("generated_html")
html_output_dir.mkdir(exist_ok=True)

def save_html_slides(html_slides_dict, output_dir, query_text):
    """Save HTML slides to individual files with metadata"""
    saved_files = []
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    for slide_key in sorted(html_slides_dict.keys(), key=lambda k: int(k.split('_')[1])):
        slide_number = int(slide_key.split('_')[1])
        slide_data = html_slides_dict[slide_key]
        html_content = slide_data.get('html_code', '')
        
        if not html_content:
            print(f"   ⚠️ No HTML content for {slide_key}, skipping...")
            continue
        
        # Determine slide type and filename
        if slide_number == 1:
            slide_type = "title"
        elif slide_number == 2:
            slide_type = "agenda"
        else:
            slide_type = "general"
        
        filename = f"slide_{slide_number}_{slide_type}.html"
        filepath = output_dir / filename
        
        # Add metadata comments to HTML
        metadata_comment = f"""<!--
Generated by: HTML-to-PptxGenJS Experimentation Notebook
Timestamp: {timestamp}
Query: {query_text[:100]}{'...' if len(query_text) > 100 else ''}
Slide: {slide_key} ({slide_type})
Input Tokens: {slide_data.get('input_token_count', 'N/A')}
Output Tokens: {slide_data.get('output_token_count', 'N/A')}
-->
"""
        
        # Combine metadata and HTML content
        full_html = metadata_comment + html_content
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(full_html)
            saved_files.append(str(filepath))
            print(f"   ✅ Saved {slide_key} ({slide_type}) → {filename}")
        except Exception as e:
            print(f"   ❌ Failed to save {slide_key}: {e}")
    
    return saved_files

def load_html_slides(input_dir):
    """Load HTML slides from files back into dictionary"""
    loaded_slides = {}
    input_path = Path(input_dir)
    
    if not input_path.exists():
        print(f"   ⚠️ Directory {input_dir} does not exist")
        return loaded_slides
    
    html_files = list(input_path.glob("slide_*.html"))
    if not html_files:
        print(f"   ⚠️ No HTML files found in {input_dir}")
        return loaded_slides
    
    for filepath in sorted(html_files):
        try:
            # Extract slide key from filename (e.g., slide_1_title.html → slide_1)
            filename = filepath.stem  # slide_1_title
            slide_key = '_'.join(filename.split('_')[:2])  # slide_1
            
            with open(filepath, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Remove metadata comments for clean HTML
            if '<!--' in html_content and '-->' in html_content:
                start = html_content.find('-->')
                if start != -1:
                    html_content = html_content[start + 3:].strip()
            
            loaded_slides[slide_key] = {
                'html_code': html_content,
                'input_token_count': 0,  # Not preserved in file
                'output_token_count': 0  # Not preserved in file
            }
            print(f"   ✅ Loaded {slide_key} from {filepath.name}")
        except Exception as e:
            print(f"   ❌ Failed to load {filepath.name}: {e}")
    
    return loaded_slides

# Save current HTML slides
if html_slides:
    saved_files = save_html_slides(html_slides, html_output_dir, query)
    print(f"\n💾 HTML persistence complete!")
    print(f"   - Saved {len(saved_files)} HTML files to: {html_output_dir}")
    print(f"   - Files: {[Path(f).name for f in saved_files]}")
else:
    print("   ⚠️ No HTML slides to save. Run Step 4a/4b/4c first.")

# Example: Load HTML slides from files (uncomment to test)
# print("\n🔄 Testing HTML loading...")
# loaded_html_slides = load_html_slides(html_output_dir)
# print(f"   - Loaded {len(loaded_html_slides)} slides: {list(loaded_html_slides.keys())}")

# Step 5: Render HTML slides to images (following Duy's pattern)
print("🖼️ Step 5: Rendering HTML slides to images...")

# Initialize rendered images dictionary
rendered_images = {}

if not html_slides:
    print("   ⚠️ No HTML slides found to render. Please run Step 4a/4b/4c first.")
else:
    print(f"Found {len(html_slides)} HTML slides to render: {list(html_slides.keys())}")
    
    # Render slides in order: slide_1, slide_2, slide_3, etc.
    for slide_key in sorted(html_slides.keys(), key=lambda k: int(k.split('_')[1])):
        slide_number = int(slide_key.split('_')[1])
        slide_data = html_slides[slide_key]
        
        # Determine slide type for better output
        if slide_number == 1:
            slide_type = "title slide"
        elif slide_number == 2:
            slide_type = "agenda slide"
        else:
            slide_type = f"general slide {slide_number}"
        
        print(f"\nRendering {slide_type} ({slide_key})...")
        
        try:
            # Extract HTML code from the slide data
            html_code = slide_data.get('html_code', '')
            
            if not html_code:
                print(f"   ⚠️ No HTML content found for {slide_key}")
                continue
            
            print(f"   - HTML length: {len(html_code)} characters")
            
            # Render HTML to image using HTMLRenderer
            rendered_image = html_renderer.renderHTML(html_code)
            
            # Store the rendered image
            rendered_images[slide_key] = rendered_image
            
            print(f"   ✅ {slide_type.capitalize()} rendered successfully!")
            
            # Show image info if available
            if hasattr(rendered_image, 'size'):
                print(f"   - Image size: {rendered_image.size}")
            elif hasattr(rendered_image, 'shape'):
                print(f"   - Image shape: {rendered_image.shape}")
            
        except Exception as e:
            print(f"   ❌ Failed to render {slide_type}: {e}")
            print(f"   - Error type: {type(e).__name__}")
            # Continue with other slides even if one fails
            continue

print(f"\n🎉 HTML rendering complete!")
print(f"   - Total slides rendered: {len(rendered_images)}")
print(f"   - Rendered slides: {list(rendered_images.keys())}")

if rendered_images:
    print(f"   - Ready for HTML-to-PptxGenJS translation testing!")
else:
    print(f"   - No slides were successfully rendered.")

# This cell has been replaced - use Phase 1 solution at the end of the notebook
print("⚠️  This cell had syntax errors and has been disabled.")
print("📋 Please scroll down to find the 'Phase 1: Individual Slide PowerPoint Generation' section.")
print("🚀 The Phase 1 solution provides clean, working individual slide generation.")
print("")
print("📍 Location: Look for the markdown header 'Phase 1: Individual Slide PowerPoint Generation'")
print("📍 It should be near the very end of this notebook.")

# Validate the slide JavaScript code before combining
print("🔍 Validating slide JavaScript code...")

def validate_slide_js(slide_name, js_code):
    """Validate that slide JavaScript code is properly formatted"""
    if not isinstance(js_code, str):
        print(f"❌ {slide_name}: Not a string (type: {type(js_code)})")
        return False
    
    if '<coroutine object' in js_code:
        print(f"❌ {slide_name}: Contains unawaited coroutine object")
        print(f"   Preview: {js_code[:100]}...")
        return False
    
    if 'function createPresentation()' not in js_code:
        print(f"❌ {slide_name}: Missing function createPresentation()")
        return False
    
    if 'const pptx = new PptxGenJS();' not in js_code:
        print(f"❌ {slide_name}: Missing PptxGenJS initialization")
        return False
    
    print(f"✅ {slide_name}: Valid JavaScript code ({len(js_code)} chars)")
    return True

# Check each slide
valid_slides = {}
if 'title_slide_js' in locals():
    if validate_slide_js('title_slide', title_slide_js):
        valid_slides['title'] = title_slide_js

if 'agenda_slide_js' in locals():
    if validate_slide_js('agenda_slide', agenda_slide_js):
        valid_slides['agenda'] = agenda_slide_js

if 'general_slide_js' in locals():
    if validate_slide_js('general_slide', general_slide_js):
        valid_slides['general'] = general_slide_js

print(f"\n📊 Valid slides for combination: {len(valid_slides)} out of {len([x for x in ['title_slide_js', 'agenda_slide_js', 'general_slide_js'] if x in locals()])}")

if not valid_slides:
    print("⚠️  No valid slides found. Please re-run the slide translation cells above with proper await statements.")
else:
    print(f"✅ Ready to combine: {', '.join(valid_slides.keys())}")

import os
import subprocess
import tempfile
import shutil
import asyncio
from datetime import datetime

# Create output directory
output_dir = os.path.join('experimentation', 'generated_presentations')
os.makedirs(output_dir, exist_ok=True)

print(f"📁 Output directory: {os.path.abspath(output_dir)}")

# Check if we have all slide translations
slides_available = []
slide_js_code = {}

if 'title_slide_js' in locals() and title_slide_js:
    slides_available.append('title')
    slide_js_code['title'] = title_slide_js
    print(f"✅ Title slide JavaScript ready ({len(title_slide_js)} chars)")

if 'agenda_slide_js' in locals() and agenda_slide_js:
    slides_available.append('agenda')
    slide_js_code['agenda'] = agenda_slide_js
    print(f"✅ Agenda slide JavaScript ready ({len(agenda_slide_js)} chars)")

if 'general_slide_js' in locals() and general_slide_js:
    slides_available.append('general')
    slide_js_code['general'] = general_slide_js
    print(f"✅ General slide JavaScript ready ({len(general_slide_js)} chars)")

print(f"\n📊 Available slides for combination: {len(slides_available)} ({', '.join(slides_available)})")

def combine_slide_js_code(slide_js_dict):
    """
    Combine individual slide JavaScript functions into a single presentation
    """
    if not slide_js_dict:
        raise ValueError("No slide JavaScript code provided")
    
    # Extract slide creation code from each function
    combined_slides = []
    
    for slide_name, js_code in slide_js_dict.items():
        print(f"🔧 Processing {slide_name} slide...")
        
        # Clean the JavaScript code
        clean_code = js_code.strip()
        
        # Extract slide creation code (between pptx creation and return statement)
        if 'const pptx = new PptxGenJS();' in clean_code and 'return pptx.writeFile' in clean_code:
            # Find the slide creation code
            start_marker = 'const pptx = new PptxGenJS();'
            end_marker = 'return pptx.writeFile'
            
            start_idx = clean_code.find(start_marker) + len(start_marker)
            end_idx = clean_code.find(end_marker)
            
            if start_idx > len(start_marker) - 1 and end_idx > start_idx:
                slide_code = clean_code[start_idx:end_idx].strip()
                
                # Clean up the slide code
                slide_code = slide_code.replace('\\n', '\\n    ')  # Proper indentation
                
                combined_slides.append(f"    // {slide_name.title()} Slide")
                combined_slides.append(f"    {slide_code}")
                combined_slides.append("")  # Empty line between slides
                
                print(f"   ✅ Extracted {len(slide_code)} characters of slide code")
            else:
                print(f"   ⚠️  Could not extract slide code from {slide_name}")
        else:
            print(f"   ⚠️  Invalid JavaScript structure in {slide_name}")
    
    # Create the complete presentation function
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_presentation_{timestamp}.pptx"
    
    combined_function = f"""function createPresentation() {{
    const pptx = new PptxGenJS();

{chr(10).join(combined_slides)}
    return pptx.writeFile({{ fileName: '{filename}' }});
}}"""
    
    return combined_function, filename

# Combine the slides if we have any valid ones
if 'valid_slides' in locals() and valid_slides:
    print(f"\n🔄 Combining {len(valid_slides)} slides into single presentation...")
    try:
        combined_js, pptx_filename = combine_slide_js_code(valid_slides)
        print(f"✅ Successfully combined slides!")
        print(f"   - Combined JavaScript: {len(combined_js)} characters")
        print(f"   - Target filename: {pptx_filename}")
    except Exception as e:
        print(f"❌ Error combining slides: {e}")
        combined_js = None
else:
    print(f"⚠️  No valid slide JavaScript code available for combination")
    print(f"   Please re-run the slide translation cells above to generate valid JavaScript code.")
    combined_js = None

def execute_combined_presentation(combined_js_code, output_dir, pptx_filename):
    """
    Execute the combined JavaScript code with Node.js to generate PowerPoint file
    """
    if not combined_js_code:
        raise ValueError("No combined JavaScript code provided")
    
    # Create complete Node.js script
    node_script = f"""const PptxGenJS = require('pptxgenjs');

{combined_js_code}

createPresentation()
    .then(() => {{
        console.log('✅ PowerPoint presentation generated successfully!');
        console.log(`📁 File saved as: {pptx_filename}`);
    }})
    .catch(error => {{
        console.error('❌ Error generating presentation:', error);
        process.exit(1);
    }});
"""
    
    # Create temporary JavaScript file
    js_filename = f"combined_presentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.js"
    js_filepath = os.path.join(output_dir, js_filename)
    
    try:
        # Write JavaScript file
        with open(js_filepath, 'w', encoding='utf-8') as f:
            f.write(node_script)
        print(f"📝 Created JavaScript file: {js_filepath}")
        
        # Execute with Node.js
        print(f"🚀 Executing Node.js script...")
        result = subprocess.run(
            ["node", js_filename],
            cwd=output_dir,
            check=True,
            capture_output=True,
            text=True,
            timeout=60  # 60 second timeout
        )
        
        print(f"✅ Node.js execution successful!")
        print(f"📄 Output: {result.stdout}")
        
        # Check if PowerPoint file was created
        pptx_filepath = os.path.join(output_dir, pptx_filename)
        if os.path.exists(pptx_filepath):
            file_size = os.path.getsize(pptx_filepath)
            print(f"🎉 PowerPoint file created successfully!")
            print(f"📁 File path: {os.path.abspath(pptx_filepath)}")
            print(f"📊 File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            return pptx_filepath
        else:
            print(f"⚠️  PowerPoint file not found at expected location: {pptx_filepath}")
            return None
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Node.js execution failed with exit code {e.returncode}")
        print(f"❌ STDOUT: {e.stdout}")
        print(f"❌ STDERR: {e.stderr}")
        return None
    except subprocess.TimeoutExpired:
        print(f"❌ Node.js execution timed out after 60 seconds")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None
    finally:
        # Clean up JavaScript file
        if os.path.exists(js_filepath):
            try:
                os.remove(js_filepath)
                print(f"🧹 Cleaned up temporary file: {js_filename}")
            except Exception as cleanup_error:
                print(f"⚠️  Could not clean up {js_filename}: {cleanup_error}")

# Execute the combined presentation if we have the code
if combined_js and 'pptx_filename' in locals():
    print(f"\n🚀 Generating PowerPoint presentation...")
    try:
        generated_file = execute_combined_presentation(combined_js, output_dir, pptx_filename)
        if generated_file:
            print(f"\n🎊 SUCCESS! Combined presentation generated:")
            print(f"   📁 Location: {generated_file}")
            print(f"   📝 You can now open this file in PowerPoint to review the slides")
        else:
            print(f"\n❌ Failed to generate PowerPoint presentation")
    except Exception as e:
        print(f"❌ Error executing combined presentation: {e}")
else:
    print(f"\n⚠️  Cannot generate PowerPoint: Missing combined JavaScript code or filename")
    if not combined_js:
        print(f"   - No combined JavaScript code available")
    if 'pptx_filename' not in locals():
        print(f"   - No PowerPoint filename defined")

# Test our validation functions
sys.path.append("html_to_pptx_experiments")

from validate_js_output import main as validate_js
from evaluate_translation import main as evaluate_translation

print("🔍 Step 4: Validating and evaluating translations...")

# Validate the generated JavaScript
print("\n📊 Validating title slide JavaScript...")
title_validation_result = validate_js(title_slide_js)
print("Title Slide Validation Results:")
print(json.dumps(title_validation_result, indent=2))

# Evaluate translation quality
print("\n📈 Evaluating title slide translation quality...")
title_quality_result = evaluate_translation(title_slide_js, title_slide_html)
print("Title Slide Quality Evaluation:")
print(json.dumps(title_quality_result, indent=2))

# Validate and evaluate agenda slide
print("📊 Validating agenda slide JavaScript...")
agenda_validation_result = validate_js(agenda_slide_js)
print("Agenda Slide Validation Results:")
print(json.dumps(agenda_validation_result, indent=2))

print("\n📈 Evaluating agenda slide translation quality...")
agenda_quality_result = evaluate_translation(agenda_slide_js, agenda_slide_html)
print("Agenda Slide Quality Evaluation:")
print(json.dumps(agenda_quality_result, indent=2))

class PromptExperimentationFramework:
    def __init__(self, llm):
        self.llm = llm
        self.results = []
    
    def test_prompt_variant(self, html_content, slide_name, prompt_template):
        """Test a specific prompt variant"""
        try:
            # Format the prompt
            formatted_prompt = prompt_template.format(
                html_content=html_content,
                slide_name=slide_name
            )
            
            # Get LLM response
            response = self.llm.call(query=formatted_prompt)
            
            # Extract JavaScript code
            if isinstance(response, dict) and "response" in response:
                js_code = response["response"]
            elif isinstance(response, dict) and "text" in response:
                js_code = response["text"]
            else:
                js_code = str(response)
            
            # Validate and evaluate
            validation = validate_js(js_code)
            quality = evaluate_translation(js_code, html_content)
            
            return {
                'js_code': js_code,
                'validation': validation,
                'quality': quality,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'success': False
            }
    
    def compare_prompts(self, html_samples, prompt_variants):
        """Compare multiple prompt variants across HTML samples"""
        results = {}
        
        for sample_name, html_content in html_samples.items():
            results[sample_name] = {}
            
            for variant_name, prompt_template in prompt_variants.items():
                print(f"Testing {variant_name} on {sample_name}...")
                
                result = self.test_prompt_variant(
                    html_content, sample_name, prompt_template
                )
                
                results[sample_name][variant_name] = result
                
                if result['success']:
                    print(f"  ✅ Success - Quality: {result['quality']['overall_quality_score']}")
                else:
                    print(f"  ❌ Failed - {result['error']}")
        
        return results

# Initialize the framework
experiment_framework = PromptExperimentationFramework(llm1)
print("🧪 Experimentation framework initialized")

# Enhancement 2: Improved Image Rendering with File Persistence
print("🖼️ Enhancement 2: Rendering HTML slides to images with file persistence...")

from pathlib import Path
from datetime import datetime

# Create output directory for rendered images
images_output_dir = Path("rendered_images")
images_output_dir.mkdir(exist_ok=True)

# Initialize rendered images dictionary
rendered_images = {}
saved_image_files = []

def save_rendered_image(image, slide_key, slide_type, output_dir):
    """Save rendered image to file with descriptive filename"""
    slide_number = int(slide_key.split('_')[1])
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"slide_{slide_number}_{slide_type}_render_{timestamp}.png"
    filepath = output_dir / filename
    
    try:
        # Save image using PIL save method
        if hasattr(image, 'save'):
            image.save(filepath)
        # Handle numpy arrays or other image formats
        elif hasattr(image, 'shape'):
            from PIL import Image
            if len(image.shape) == 3:  # RGB image
                pil_image = Image.fromarray(image)
                pil_image.save(filepath)
            else:
                print(f"   ⚠️ Unsupported image format for {slide_key}")
                return None
        else:
            print(f"   ⚠️ Unknown image type for {slide_key}: {type(image)}")
            return None
        
        return str(filepath)
    except Exception as e:
        print(f"   ❌ Failed to save image for {slide_key}: {e}")
        return None

if not html_slides:
    print("   ⚠️ No HTML slides found to render. Please run Step 4a/4b/4c first.")
else:
    print(f"Found {len(html_slides)} HTML slides to render: {list(html_slides.keys())}")
    print(f"Images will be saved to: {images_output_dir}")
    
    # Render slides in order: slide_1, slide_2, slide_3, etc.
    for slide_key in sorted(html_slides.keys(), key=lambda k: int(k.split('_')[1])):
        slide_number = int(slide_key.split('_')[1])
        slide_data = html_slides[slide_key]
        
        # Determine slide type for better output
        if slide_number == 1:
            slide_type = "title"
            slide_type_display = "title slide"
        elif slide_number == 2:
            slide_type = "agenda"
            slide_type_display = "agenda slide"
        else:
            slide_type = "general"
            slide_type_display = f"general slide {slide_number}"
        
        print(f"\nRendering {slide_type_display} ({slide_key})...")
        
        try:
            # Extract HTML code from the slide data
            html_code = slide_data.get('html_code', '')
            
            if not html_code:
                print(f"   ⚠️ No HTML content found for {slide_key}")
                continue
            
            print(f"   - HTML length: {len(html_code)} characters")
            
            # Render HTML to image using HTMLRenderer
            rendered_image = html_renderer.renderHTML(html_code)
            
            # Store the rendered image in memory
            rendered_images[slide_key] = rendered_image
            
            # Save the rendered image to file
            saved_filepath = save_rendered_image(rendered_image, slide_key, slide_type, images_output_dir)
            if saved_filepath:
                saved_image_files.append(saved_filepath)
                print(f"   ✅ {slide_type_display.capitalize()} rendered and saved!")
                print(f"   - Saved to: {Path(saved_filepath).name}")
            else:
                print(f"   ⚠️ {slide_type_display.capitalize()} rendered but not saved to file")
            
            # Show image info if available
            if hasattr(rendered_image, 'size'):
                print(f"   - Image size: {rendered_image.size}")
            elif hasattr(rendered_image, 'shape'):
                print(f"   - Image shape: {rendered_image.shape}")
            
        except Exception as e:
            print(f"   ❌ Failed to render {slide_type_display}: {e}")
            print(f"   - Error type: {type(e).__name__}")
            # Continue with other slides even if one fails
            continue

print(f"\n🎉 Enhanced image rendering complete!")
print(f"   - Total slides rendered: {len(rendered_images)}")
print(f"   - Rendered slides: {list(rendered_images.keys())}")
print(f"   - Total image files saved: {len(saved_image_files)}")
print(f"   - Saved to directory: {images_output_dir}")

if saved_image_files:
    print(f"   - Image files: {[Path(f).name for f in saved_image_files]}")

if rendered_images:
    print(f"   - Ready for HTML-to-PptxGenJS translation testing!")
else:
    print(f"   - No slides were successfully rendered.")

# Setup for Phase 1: Individual slide generation
import os
import shutil
from datetime import datetime
import importlib
import asyncio

# Force reload the translator module to get the latest version
import pptx_generation.html_to_pptx_translator as h2p
importlib.reload(h2p)

# Create output directory
out_dir = os.path.join('experimentation', 'generated_presentations')
os.makedirs(out_dir, exist_ok=True)
print(f"📁 Output directory: {os.path.abspath(out_dir)}")
print(f"🔍 Translator function is async: {asyncio.iscoroutinefunction(h2p.llm_html_to_pptxgenjs_single)}")

# Helper function to translate and generate a single slide PPTX
async def translate_and_generate_single(html_text: str, slide_key: str, llm):
    """Translate HTML to JS and generate individual PPTX file"""
    try:
        print(f"  🔄 Translating {slide_key}...")
        js_code = await h2p.llm_html_to_pptxgenjs_single(html_text, slide_key, llm)
        
        # Check for coroutine objects in the generated code
        if '<coroutine object' in js_code:
            raise ValueError(f"Generated JavaScript contains unawaited coroutine objects")
        
        print(f"  ✅ Translation complete ({len(js_code)} chars)")
        print(f"  🚀 Generating PowerPoint file...")
        
        # Execute the JavaScript to create PPTX
        result = h2p.execute_pptxgenjs_code(js_code)
        temp_path = result['pptx_file_path']
        
        # Copy to our output directory with descriptive filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{slide_key}_{timestamp}.pptx"
        final_path = os.path.join(out_dir, filename)
        shutil.copy2(temp_path, final_path)
        
        file_size = os.path.getsize(final_path)
        print(f"  🎉 Success! File: {filename} ({file_size:,} bytes)")
        
        return os.path.abspath(final_path)
        
    except Exception as e:
        print(f"  ❌ Error generating {slide_key}: {e}")
        return None

print("✅ Helper function defined")

# Generate individual PPTX files for each slide
print("🚀 Phase 1: Generating individual slide PowerPoint files...")

# Choose an LLM (consistent with prior cells)
gen_llm = llm2 if 'llm2' in globals() else LLM(provider="gemini", model="gemini-2.5-flash")

results = {}
if 'slide_1' in html_slides:
    print("\n📄 Generating title slide PPTX...")
    results['title'] = await translate_and_generate_single(html_slides['slide_1']['html_code'], "title_slide", gen_llm)

if 'slide_2' in html_slides:
    print("\n📋 Generating agenda slide PPTX...")
    results['agenda'] = await translate_and_generate_single(html_slides['slide_2']['html_code'], "agenda_slide", gen_llm)

if 'slide_3' in html_slides:
    print("\n📊 Generating general slide PPTX...")
    results['general'] = await translate_and_generate_single(html_slides['slide_3']['html_code'], "general_slide", gen_llm)

print(f"\n🎊 Phase 1 Complete! Generated {len([r for r in results.values() if r])} PowerPoint files.")

# Verification: Check generated files
print("📋 File Verification:")
print("=" * 80)
for slide_type, file_path in results.items():
    if file_path:
        exists = os.path.exists(file_path)
        size = os.path.getsize(file_path) if exists else 0
        status = "✅ SUCCESS" if exists and size > 1000 else "❌ FAILED"
        print(f"{slide_type:8s} | {status} | {size:,} bytes")
        print(f"         | {file_path}")
    else:
        print(f"{slide_type:8s} | ❌ FAILED | Generation failed")
    print("-" * 80)

successful_files = [f for f in results.values() if f and os.path.exists(f)]
print(f"\n🎯 Summary: {len(successful_files)} out of {len(results)} slides generated successfully")
if successful_files:
    print("\n📁 You can now open these PowerPoint files to review the individual slides!")
else:
    print("\n⚠️  No files were generated successfully. Check the error messages above.")