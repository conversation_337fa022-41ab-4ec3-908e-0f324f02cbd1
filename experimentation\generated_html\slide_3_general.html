<!--
Generated by: HTML-to-PptxGenJS Experimentation Notebook
Timestamp: 2025-08-10 15:40:24
Query: Make me a presentation about building a system which takes an internal company database of contract ...
Slide: slide_3 (general)
Input Tokens: 2021
Output Tokens: 1560
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Challenge: Navigating Contract Data</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .slide-container {
            width: 1280px; /* 720p width */
            height: 720px; /* 720p height */
            background-color: #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 40px;
            box-sizing: border-box;
        }
        .section-title {
            font-size: 2.25rem; /* 36px */
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            display: inline-block;
        }
        .subsection-title {
            font-size: 1.75rem; /* 28px */
            font-weight: 600;
            color: #34495e;
            margin-bottom: 15px;
        }
        .list-item {
            font-size: 1.15rem; /* ~18px */
            color: #555;
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
        }
        .list-item::before {
            content: '•';
            color: #3498db;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
            margin-right: 0.5em;
        }
        .icon-box {
            width: 100px;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #ecf0f1;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .icon-box img {
            max-width: 70%;
            max-height: 70%;
            object-fit: contain;
        }
    


/* AUTO-FIT CSS TEMPLATE - Ensures content fits within 720px height */
body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
    position: relative;
    background-color: #FFFFFF;
    font-family: 'Roboto', sans-serif;
    overflow: hidden;
    color: #272774;
    display: flex;
    flex-direction: column;
}

/* Main content container with auto-fit */
.slide-content {
    position: absolute;
    top: 60px;
    left: 60px;
    right: 60px;
    bottom: 80px; /* Leave space for footer */
    max-width: 1160px;
    max-height: 580px; /* 720 - 60 (top) - 80 (bottom) */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1;
}

/* Auto-scaling title */
.slide-title {
    font-size: clamp(1.8em, 4vw, 2.3em); /* Responsive title size */
    font-weight: 700;
    margin-bottom: clamp(15px, 3vh, 30px);
    color: #272774;
    flex-shrink: 0; /* Don't shrink title */
    line-height: 1.2;
}

/* Content area that auto-fits remaining space */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* Allow flex shrinking */
}

/* Split content layout */
.split-content {
    display: flex;
    gap: clamp(20px, 3vw, 40px);
    flex: 1;
    min-height: 0;
}

/* Sections that auto-scale */
.visual-section, .bullet-points-section {
    flex: 1;
    background: #F5E2DA;
    border-radius: 8px;
    padding: clamp(10px, 2vh, 20px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

/* Auto-scaling text elements - Improved readability */
.content-text {
    font-size: clamp(1.0em, 1.5vw, 1.2em);  /* Increased min from 0.8em to 1.0em */
    line-height: 1.4;
    margin-bottom: clamp(8px, 1.5vh, 15px);
}

/* Bullet points with auto-scaling - Better readability */
ul {
    margin: 0;
    padding-left: 20px;
    flex: 1;
    overflow: hidden;
}

li {
    font-size: clamp(0.9em, 1.4vw, 1.1em);  /* Increased min from 0.75em to 0.9em */
    line-height: 1.5;
    margin-bottom: clamp(5px, 1vh, 10px);
    display: flex;
    align-items: flex-start;
}

/* Icon sizing */
.icon {
    margin-right: 10px;
    flex-shrink: 0;
    font-size: clamp(0.8em, 1.2vw, 1em);
    padding-top: 2px;
}

/* Image placeholders that scale */
.image-placeholder {
    position: relative;
    width: 100%;
    height: clamp(120px, 25vh, 200px);
    background: #FFFFFF;
    border: 2px solid #272774;
    border-radius: 8px;
    margin-bottom: clamp(10px, 2vh, 20px);
    flex-shrink: 0;
}

/* Progress bars and other elements */
.progress-bar-container {
    margin-top: auto;
    padding-top: clamp(5px, 1vh, 10px);
}

/* Fixed elements (logo, page number) */
.page-number {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #EBC4B4;
    color: #272774;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    font-size: clamp(12px, 1.2vw, 14px);
    z-index: 10;
}

.logo {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

.logo img {
    height: clamp(40px, 6vh, 60px);
}

/* Responsive adjustments for content-heavy slides */
@media (max-height: 720px) {
    .slide-title {
        font-size: 1.8em;
        margin-bottom: 15px;
    }
    
    .content-text, li {
        font-size: 0.9em;  /* Increased from 0.8em */
        line-height: 1.3;
        margin-bottom: 8px;
    }
    
    .visual-section, .bullet-points-section {
        padding: 15px;
    }
    
    .image-placeholder {
        height: 120px;
        margin-bottom: 10px;
    }
}

/* Utility classes for different content densities - Improved readability */
.content-light .slide-title { font-size: 2.5em; }
.content-light .content-text { font-size: 1.1em; }
.content-light li { font-size: 1em; }

.content-medium .slide-title { font-size: 2.2em; }
.content-medium .content-text { font-size: 1.0em; }  /* Increased from 0.95em */
.content-medium li { font-size: 0.9em; }  /* Increased from 0.85em */

.content-heavy .slide-title { font-size: 2.1em; margin-bottom: 15px; }  /* Increased from 1.9em */
.content-heavy .content-text { font-size: 0.9em; line-height: 1.3; }  /* Increased from 0.8em */
.content-heavy li { font-size: 0.85em; line-height: 1.4; margin-bottom: 6px; }  /* Increased from 0.75em */
.content-heavy .visual-section, .content-heavy .bullet-points-section { padding: 12px; }
.content-heavy .image-placeholder { height: 100px; margin-bottom: 8px; }
</style>
</head>
<body class="content-light">
    <div class="slide-container">
        <h1 class="section-title">The Challenge: Navigating Contract Data</h1>

        <div class="flex flex-grow gap-10 mt-8">
            <!-- The Problem Section -->
            <div class="w-1/3 flex flex-col items-center text-center">
                <div class="icon-box">
                    <img src="https://static.vecteezy.com/system/resources/previews/023/684/476/original/document-search-icon-design-template-free-vector.jpg" alt="Manual Search Icon">
                </div>
                <h2 class="subsection-title">The Problem: Manual, Time-Consuming Searches</h2>
                <ul class="text-left w-full px-4">
                    <li class="list-item"><strong>Inefficient Retrieval</strong>: Employees spend excessive time sifting through vast digital archives.</li>
                    <li class="list-item"><strong>Keyword Limitations</strong>: Traditional search often misses context, leading to irrelevant results.</li>
                    <li class="list-item"><strong>Version Control Issues</strong>: Difficulty in identifying the most current or relevant contract version.</li>
                </ul>
            </div>

            <!-- Impact Section -->
            <div class="w-1/3 flex flex-col items-center text-center">
                <div class="icon-box">
                    <img src="https://cdn4.iconfinder.com/data/icons/zeir-time-events-vol-1/25/waste_of_time_wasting_time-1024.png" alt="Time Wasted Icon">
                </div>
                <h2 class="subsection-title">Impact: Inefficiency & Missed Opportunities</h2>
                <ul class="text-left w-full px-4">
                    <li class="list-item"><strong>Delayed Decision-Making</strong>: Slow access to critical information hinders business agility.</li>
                    <li class="list-item"><strong>Increased Operational Costs</strong>: Wasted labor hours on administrative tasks.</li>
                    <li class="list-item"><strong>Compliance Risks</strong>: Potential for using outdated or incorrect contract terms.</li>
                </ul>
            </div>

            <!-- The Need Section -->
            <div class="w-1/3 flex flex-col items-center text-center">
                <div class="icon-box">
                    <img src="https://static.vecteezy.com/system/resources/previews/017/438/197/non_2x/smart-search-logo-and-icon-playful-logo-featuring-a-magnifying-glass-which-is-also-a-smart-vector.jpg" alt="Smart Search Icon">
                </div>
                <h2 class="subsection-title">The Need: A Smarter Approach to Information Retrieval</h2>
                <ul class="text-left w-full px-4">
                    <li class="list-item"><strong>Automated Contextual Search</strong>: Beyond keywords to understanding intent.</li>
                    <li class="list-item"><strong>Centralized & Accessible</strong>: A single, intuitive platform for all contract documents.</li>
                    <li class="list-item"><strong>Enhanced Accuracy</strong>: Ensuring the right document is found every time.</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>