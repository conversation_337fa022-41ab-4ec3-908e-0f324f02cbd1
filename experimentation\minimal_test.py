#!/usr/bin/env python3
"""
Minimal test for HTML-to-PptxGenJS translation without full LLM dependencies
"""

import sys
import os
import json

# Add paths
sys.path.append("../")
sys.path.append("html_to_pptx_experiments")

def test_minimal_setup():
    """Test minimal setup without LLM dependencies"""
    print("🧪 Testing Minimal HTML-to-PptxGenJS Setup")
    print("=" * 50)
    
    # Test validation functions
    print("1. Testing validation functions...")
    try:
        from validate_js_output import main as validate_js
        
        # Test JavaScript sample
        test_js = """
        function createPresentation() {
            const pptx = new PptxGenJS();
            const slide = pptx.addSlide();
            slide.background = { color: '2C3E50' };
            slide.addText('Test Title', {
                x: 1.0, y: 0.8, w: 8.0, h: 1.0,
                fontSize: 32, color: 'FFFFFF', bold: true
            });
            return pptx.writeFile({ fileName: 'presentation.pptx' });
        }
        """
        
        validation_result = validate_js(test_js)
        print(f"   ✅ Validation successful:")
        print(f"      - Syntax valid: {validation_result.get('syntax_valid', False)}")
        print(f"      - Positioning valid: {validation_result.get('positioning_valid', False)}")
        print(f"      - Colors valid: {validation_result.get('colors_valid', False)}")
        print(f"      - Overall valid: {validation_result.get('overall_valid', False)}")
        
    except Exception as e:
        print(f"   ❌ Validation failed: {e}")
        return False
    
    # Test quality evaluation
    print("\n2. Testing quality evaluation...")
    try:
        from evaluate_translation import main as evaluate_translation
        
        # Test HTML sample
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { background-color: #2C3E50; color: #FFFFFF; }
                .title { font-size: 2em; color: #FFFFFF; }
            </style>
        </head>
        <body>
            <h1 class="title">Test Title</h1>
            <p>Test content</p>
        </body>
        </html>
        """
        
        quality_result = evaluate_translation(test_js, test_html)
        print(f"   ✅ Quality evaluation successful:")
        print(f"      - Color extraction: {quality_result.get('color_extraction_score', 0)}/100")
        print(f"      - Positioning: {quality_result.get('positioning_score', 0)}/100")
        print(f"      - Content preservation: {quality_result.get('content_preservation_score', 0)}/100")
        print(f"      - Overall quality: {quality_result.get('overall_quality_score', 0)}/100")
        
    except Exception as e:
        print(f"   ❌ Quality evaluation failed: {e}")
        return False
    
    # Test prompt files
    print("\n3. Testing prompt files...")
    prompt_dir = "html_to_pptx_experiments/prompts"
    prompt_files = ["baseline.jinja2", "enhanced_positioning.jinja2", "improved_colors.jinja2", "comprehensive_v2.jinja2"]
    
    for prompt_file in prompt_files:
        prompt_path = os.path.join(prompt_dir, prompt_file)
        if os.path.exists(prompt_path):
            size = os.path.getsize(prompt_path)
            print(f"   ✅ {prompt_file} exists ({size} bytes)")
        else:
            print(f"   ❌ {prompt_file} missing")
    
    # Test basic HTML-to-JS translation (mock)
    print("\n4. Testing translation framework...")
    try:
        # Mock translation test
        def mock_translate_html_to_js(html_content, slide_name):
            """Mock translation function"""
            return """
            function createPresentation() {
                const pptx = new PptxGenJS();
                const slide = pptx.addSlide();
                slide.background = { color: '2C3E50' };
                slide.addText('Mock Title', {
                    x: 1.0, y: 0.8, w: 8.0, h: 1.0,
                    fontSize: 32, color: 'FFFFFF', bold: true
                });
                return pptx.writeFile({ fileName: 'presentation.pptx' });
            }
            """
        
        # Test mock translation
        mock_html = "<h1>Test</h1>"
        mock_js = mock_translate_html_to_js(mock_html, "test_slide")
        
        # Validate mock result
        mock_validation = validate_js(mock_js)
        mock_quality = evaluate_translation(mock_js, mock_html)
        
        print(f"   ✅ Mock translation successful:")
        print(f"      - Generated {len(mock_js)} characters of JavaScript")
        print(f"      - Validation passed: {mock_validation.get('overall_valid', False)}")
        print(f"      - Quality score: {mock_quality.get('overall_quality_score', 0)}/100")
        
    except Exception as e:
        print(f"   ❌ Translation framework test failed: {e}")
        return False
    
    print("\n🎉 Minimal test complete!")
    print("=" * 50)
    print("✅ Core validation and evaluation system is working!")
    print("\nNext steps:")
    print("1. Install tavily: poetry add tavily-python")
    print("2. Set up API keys for full LLM testing")
    print("3. Run full test: poetry run python test_prompt_flow.py")
    print("4. Or start with notebook: jupyter notebook prompt_flow_experimentation.ipynb")
    
    return True

if __name__ == "__main__":
    test_minimal_setup()
