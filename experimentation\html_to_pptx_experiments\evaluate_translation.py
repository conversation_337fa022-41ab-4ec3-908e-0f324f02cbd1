"""
Quality Evaluation for HTML-to-PptxGenJS Translation
"""
import re
from typing import Dict, Any, List
from bs4 import BeautifulSoup


def extract_html_colors(html_content: str) -> List[str]:
    """Extract colors from HTML content"""
    colors = []
    
    # Extract from inline styles
    inline_colors = re.findall(r'color:\s*#?([A-Fa-f0-9]{3,6})', html_content)
    colors.extend(inline_colors)
    
    # Extract from background-color
    bg_colors = re.findall(r'background-color:\s*#?([A-Fa-f0-9]{3,6})', html_content)
    colors.extend(bg_colors)
    
    return list(set(colors))  # Remove duplicates


def extract_js_colors(js_code: str) -> List[str]:
    """Extract colors from JavaScript code"""
    colors = []
    
    # Extract color values
    color_patterns = re.findall(r"color:\s*['\"]([A-Fa-f0-9]{3,6})['\"]", js_code)
    colors.extend(color_patterns)
    
    # Extract background colors
    bg_patterns = re.findall(r"color:\s*['\"]([A-Fa-f0-9]{3,6})['\"]", js_code)
    colors.extend(bg_patterns)
    
    return list(set(colors))


def evaluate_color_extraction(js_code: str, html_input: str) -> int:
    """
    Evaluate how well colors were extracted from HTML
    Returns score 0-100
    """
    try:
        html_colors = extract_html_colors(html_input)
        js_colors = extract_js_colors(js_code)
        
        if not html_colors:
            # If no colors in HTML, check if reasonable defaults were used
            return 80 if js_colors else 60
        
        # Calculate overlap
        html_colors_normalized = [c.upper().zfill(6) for c in html_colors]
        js_colors_normalized = [c.upper().zfill(6) for c in js_colors]
        
        overlap = len(set(html_colors_normalized) & set(js_colors_normalized))
        total_html_colors = len(html_colors_normalized)
        
        if total_html_colors == 0:
            return 70  # Default score if no colors to extract
        
        score = int((overlap / total_html_colors) * 100)
        return min(100, max(0, score))
        
    except Exception as e:
        print(f"Color evaluation error: {e}")
        return 0


def evaluate_positioning_quality(js_code: str) -> int:
    """
    Evaluate positioning quality
    Returns score 0-100
    """
    try:
        score = 100
        
        # Extract positions
        x_positions = [float(x) for x in re.findall(r'x:\s*([0-9]+\.?[0-9]*)', js_code)]
        y_positions = [float(y) for y in re.findall(r'y:\s*([0-9]+\.?[0-9]*)', js_code)]
        
        # Check bounds
        for x in x_positions:
            if x < 0.5 or x > 9.5:
                score -= 20
        
        for y in y_positions:
            if y < 0.5 or y > 5.0:
                score -= 20
        
        # Check for overlapping positions (within 0.5 inches)
        for i, y1 in enumerate(y_positions):
            for y2 in y_positions[i+1:]:
                if abs(y1 - y2) < 0.5:
                    score -= 15  # Penalty for overlapping
        
        # Check for reasonable title positioning (should be near top)
        title_positions = []
        addtext_matches = re.findall(r'addText\([^}]*y:\s*([0-9.]+)[^}]*fontSize:\s*([0-9]+)', js_code)
        for y_str, font_str in addtext_matches:
            y = float(y_str)
            font = int(font_str)
            if font >= 28:  # Likely a title
                title_positions.append(y)
        
        # Titles should be in upper portion (y < 2.0)
        for title_y in title_positions:
            if title_y > 2.0:
                score -= 10
        
        return max(0, min(100, score))
        
    except Exception as e:
        print(f"Positioning evaluation error: {e}")
        return 0


def evaluate_content_preservation(js_code: str, html_input: str) -> int:
    """
    Evaluate how well content was preserved
    Returns score 0-100
    """
    try:
        # Extract text from HTML
        soup = BeautifulSoup(html_input, 'html.parser')
        html_text = soup.get_text().strip()
        html_words = set(html_text.lower().split())
        
        # Extract text from JavaScript
        js_text_matches = re.findall(r"addText\(['\"]([^'\"]*)['\"]", js_code)
        js_text = ' '.join(js_text_matches)
        js_words = set(js_text.lower().split())
        
        if not html_words:
            return 50  # Default if no text to compare
        
        # Calculate word overlap
        overlap = len(html_words & js_words)
        total_html_words = len(html_words)
        
        score = int((overlap / total_html_words) * 100)
        return min(100, max(0, score))
        
    except Exception as e:
        print(f"Content evaluation error: {e}")
        return 0


def main(js_code: str, html_input: str) -> Dict[str, Any]:
    """
    Main evaluation function called by Prompt Flow
    """
    results = {
        'color_extraction_score': evaluate_color_extraction(js_code, html_input),
        'positioning_score': evaluate_positioning_quality(js_code),
        'content_preservation_score': evaluate_content_preservation(js_code, html_input)
    }
    
    # Calculate overall quality score
    results['overall_quality_score'] = int(
        (results['color_extraction_score'] + 
         results['positioning_score'] + 
         results['content_preservation_score']) / 3
    )
    
    return results


if __name__ == "__main__":
    # For testing
    test_html = """
    <html>
    <head><style>body { background-color: #2C3E50; }</style></head>
    <body>
        <h1 style="color: #FFFFFF;">Test Title</h1>
        <p style="color: #E0E0E0;">Test content here</p>
    </body>
    </html>
    """
    
    test_js = """
    function createPresentation() {
        const pptx = new PptxGenJS();
        const slide = pptx.addSlide();
        slide.background = { color: '2C3E50' };
        slide.addText('Test Title', {
            x: 1.0, y: 0.8, w: 8.0, h: 1.0,
            fontSize: 32, color: 'FFFFFF', bold: true
        });
        slide.addText('Test content here', {
            x: 1.0, y: 2.0, w: 8.0, h: 1.0,
            fontSize: 16, color: 'E0E0E0'
        });
        return pptx.writeFile({ fileName: 'presentation.pptx' });
    }
    """
    
    result = main(test_js, test_html)
    print(f"Evaluation result: {result}")
