You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code with ADVANCED COLOR EXTRACTION.

ADVANCED COLOR EXTRACTION SYSTEM:

**STEP 1: COMPREHENSIVE COLOR ANALYSIS**
Analyze HTML for colors in this priority order:
1. **Inline styles**: style="color: #FF0000; background-color: #0000FF"
2. **CSS classes**: .title { color: #333333; }, .bg-blue-500, .text-white
3. **CSS variables**: --primary-color: #2C3E50; var(--primary-color)
4. **Computed styles**: body { background: linear-gradient(...) }
5. **Theme colors**: data-theme="dark", class="theme-corporate"

**STEP 2: COLOR MAPPING PATTERNS**
```
CSS Pattern → PptxGenJS Format
#FF0000 → 'FF0000'
rgb(255, 0, 0) → 'FF0000'  
rgba(255, 0, 0, 0.8) → 'FF0000'
hsl(0, 100%, 50%) → 'FF0000'
red → 'FF0000'
--primary-color: #2C3E50 → '2C3E50'
background-color: #f8f9fa → 'F8F9FA'
```

**STEP 3: INTELLIGENT COLOR FALLBACKS**
If extraction fails, use professional defaults:
- Dark theme: bg='1A1A1A', text='E0E0E0', accent='88CCEE'
- Light theme: bg='FFFFFF', text='2C3E50', accent='3B82F6'
- Corporate: bg='0D1B2A', text='FFFFFF', accent='00ADB5'

**STEP 4: COLOR VALIDATION & CLEANUP**
- Ensure 6-digit hex: 'FFF' → 'FFFFFF'
- Remove invalid characters: '#FF0000!' → 'FF0000'
- Validate hex pattern: /^[A-Fa-f0-9]{6}$/
- Convert to uppercase: 'ff0000' → 'FF0000'

**STEP 5: DYNAMIC COLOR ASSIGNMENT**
```javascript
// Extract background color
const bgColor = extractBackgroundColor(htmlContent) || 'FFFFFF';

// Extract text colors by element type
const titleColor = extractTextColor('.title, h1') || '2C3E50';
const subtitleColor = extractTextColor('.subtitle, h2') || '4B5563';
const contentColor = extractTextColor('p, .content') || '374151';
const accentColor = extractAccentColor('.accent, .primary') || '3B82F6';
```

**STEP 6: GENERATE PPTXGENJS WITH EXTRACTED COLORS**
```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Use extracted background color
    slide.background = { color: 'EXTRACTED_BG_COLOR' };
    
    // Use extracted text colors for each element
    slide.addText('Title Text', {
        x: 1.0, y: 0.8, w: 8.0, h: 0.8,
        fontSize: 36, color: 'EXTRACTED_TITLE_COLOR', bold: true
    });
    
    slide.addText('Subtitle Text', {
        x: 1.0, y: 1.4, w: 8.0, h: 0.6,
        fontSize: 24, color: 'EXTRACTED_SUBTITLE_COLOR'
    });
    
    slide.addText('Content Text', {
        x: 1.0, y: 2.0, w: 8.0, h: 0.6,
        fontSize: 16, color: 'EXTRACTED_CONTENT_COLOR'
    });
    
    return pptx.writeFile({ fileName: 'presentation.pptx' });
}
```

**COLOR EXTRACTION EXAMPLES:**
```html
<!-- Example 1: Inline styles -->
<div style="background-color: #0a0f1f; color: #ffffff;">
→ bg: '0A0F1F', text: 'FFFFFF'

<!-- Example 2: CSS classes -->
<h1 class="text-blue-600 bg-gray-900">
→ text: '2563EB', bg: '111827'

<!-- Example 3: CSS variables -->
<style>:root { --primary: #2C3E50; }</style>
<div style="color: var(--primary);">
→ text: '2C3E50'
```

**CRITICAL FORMATTING RULES:**
- Colors MUST be quoted strings: color: 'FF0000'
- NO hash symbols: '#FF0000' is WRONG
- NO variables: BG_COLOR is WRONG  
- NO backslashes: 'FF0000\' is WRONG
- CORRECT format: slide.background = { color: 'FF0000' };

HTML to convert:
{{html_content}}

Slide name: {{slide_name}}

Generate JavaScript with PERFECT color extraction and formatting.
