# app.py (Updated with CORS and a bug fix)

from fastapi import Fast<PERSON><PERSON>, HTTPException
from pydantic import BaseModel
# 🔽 --- 1. ADD THIS IMPORT --- 🔽
from fastapi.middleware.cors import CORSMiddleware
from pptx_generation.planner_official import Planner
from pptx_generation.generation_official import Generator
from htmlrender.renderer import H<PERSON>LRenderer
from llm.llmwrapper_official import LLM
from pptx_generation.html_to_pptx_translator import generate_powerpoint_from_html
from dotenv import load_dotenv
import traceback
import os
from datetime import datetime
from fastapi.responses import FileResponse

load_dotenv()

class UserInput(BaseModel):
    user_input: str

class OutlineInput(BaseModel):
    outline: str
    user_input: str = "Create a presentation"  # Optional, with default

class SlidesInput(BaseModel):
    slides: list
    user_input: str = "Create a presentation"  # Optional, with default

class HTMLInput(BaseModel):
    html_slides: list

class PresentationResponse(BaseModel):
    outline: str = None
    slides: list = None
    html_slides: list = None
    presentation_js: str = None
    download_url: str = None
    status: str
    message: str

app = FastAPI(
    title="SlideGen API",
    description="Generate slide outlines and rendered HTML from a user query",
    version="0.1",
    debug=True
)

# 🔽 --- 2. ADD THIS ENTIRE MIDDLEWARE BLOCK --- 🔽
# This allows your frontend (e.g., at http://127.0.0.1:5500) to communicate with your backend API.
origins = ["*"]  # This is a wildcard, good for local development.

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],  # Allows POST, GET, etc.
    allow_headers=["*"],
)
# 🔼 --- END OF MIDDLEWARE BLOCK --- 🔼

# Initialize your LLMs, planner, generator, renderer once
llm_outline = LLM(provider="gemini", model="gemini-2.5-flash") # Note: Adjusted model name from your example. Change if needed.
llm_generate = LLM(provider="gemini", model="gemini-2.5-pro") # Note: Adjusted model name from your example. Change if needed.
llm_translate = LLM(provider="gemini", model="gemini-2.5-flash") # For HTML-to-PptxGenJS translation
planner = Planner()
generator = Generator()
renderer = HTMLRenderer()

@app.post("/generate-outline")
async def generate_outline_endpoint(req: UserInput):
    try:
        print(f"✅ API call received for outline generation. Input: '{req.user_input}'") # Added for logging
        
        # 🐞 --- BUG FIX --- 🐞
        # You need to pass the actual user input string, not the Pydantic class type.
        # OLD: query=UserInput
        # NEW: query=req.user_input
        pptx_plan = planner.outline(query=req.user_input, llm=llm_outline)
        
        # The planner.outline function likely returns a dictionary.
        # Make sure the key "response" exists and contains the string you want to return.
        if "response" in pptx_plan and isinstance(pptx_plan["response"], str):
            return pptx_plan["response"]
        else:
            # If the response format is not what you expect, raise an error.
            print(f"❌ Unexpected response format from planner: {pptx_plan}")
            raise HTTPException(status_code=500, detail="Internal server error: Invalid format from planner.")
            
    except Exception as e:
        # It's good practice to log the full traceback for debugging
        print(f"❌ An error occurred: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate-slides")
async def generate_slides_endpoint(req: OutlineInput):
    try:
        print(f"✅ API call received for slide generation. Outline length: {len(req.outline)} chars")

        # Use the proper planner to generate detailed slide content
        user_query = req.user_input

        # Use planner to generate detailed slide content from the outline
        # The planner expects brainstorm + outline, but we already have the outline
        # So we'll create a minimal brainstorm response
        brainstorm_response = "Technical approach to create a comprehensive presentation."

        slide_content_output = planner.slide_content(
            query=user_query,
            brainstorm_response=brainstorm_response,
            outline_response=req.outline,
            llm=llm_generate
        )

        # Extract structured slide content
        processed_slide_content = planner.extract_slide_content(
            slide_content_response=slide_content_output['response']
        )

        # Convert to the expected format for the API
        slides = []
        slide_content_dict = processed_slide_content['slide_content']

        for slide_key, slide_content in slide_content_dict.items():
            slide_number = int(slide_key.split('_')[1])  # Extract number from 'slide_1', 'slide_2', etc.

            slide_data = {
                "slide_number": slide_number,
                "title": slide_content.split('\n')[0] if slide_content else f"Slide {slide_number}",
                "content": slide_content,
                "slide_type": "title" if slide_number == 1 else "content"
            }
            slides.append(slide_data)

        # Sort slides by slide number
        slides.sort(key=lambda x: x['slide_number'])

        print(f"✅ Generated {len(slides)} slides using planner")

        return PresentationResponse(
            outline=req.outline,
            slides=slides,
            status="success",
            message=f"Generated {len(slides)} slides using planner"
        )

    except Exception as e:
        print(f"❌ An error occurred in slide generation: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/render-html")
async def render_html_endpoint(req: SlidesInput):
    """Convert slide content to HTML using the Generator class"""
    try:
        print(f"✅ API call received for HTML rendering. {len(req.slides)} slides to render")

        html_slides = []
        existing_slide_content = []
        user_query = req.user_input

        for i, slide in enumerate(req.slides):
            try:
                slide_number = slide.get('slide_number', i+1)
                slide_content = slide.get('content', 'No content available')

                # Generate HTML based on slide type
                html_content = _generate_slide_html(
                    slide_number, slide_content, user_query, existing_slide_content
                )

                # Build result
                slide_data = {
                    "slide_number": slide_number,
                    "html_content": html_content,
                    "original_slide": slide
                }
                html_slides.append(slide_data)

                # Track for context in subsequent slides
                existing_slide_content.append({
                    'name': f'Slide {slide_number}',
                    'html': html_content
                })

                print(f"✅ Rendered slide {slide_number}: {slide.get('title', f'Slide {slide_number}')}")

            except Exception as slide_error:
                print(f"❌ Error rendering slide {i+1}: {slide_error}")
                traceback.print_exc()

                # Add error slide but continue processing
                html_slides.append({
                    "slide_number": i+1,
                    "html_content": f"<div style='padding: 20px; color: red;'>Error rendering slide {i+1}: {str(slide_error)}</div>",
                    "original_slide": slide,
                    "error": str(slide_error)
                })

        return PresentationResponse(
            slides=req.slides,
            html_slides=html_slides,
            status="success",
            message=f"Rendered {len(html_slides)} HTML slides successfully"
        )

    except Exception as e:
        print(f"❌ An error occurred in HTML rendering: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


def _generate_slide_html(slide_number: int, slide_content: str, user_query: str, existing_slide_content: list) -> str:
    """Generate HTML for a single slide using the appropriate Generator method"""
    common_params = {
        'query': user_query,
        'slide_content': slide_content,
        'generator_llm': llm_generate,
        'reviewer_llm': llm_generate,
        'review': False  # Skip review for faster processing
    }

    if slide_number == 1:
        return generator.generate_title_slide(**common_params)
    elif slide_number == 2:
        title_slide_html = existing_slide_content[0]['html'] if existing_slide_content else ""
        return generator.generate_agenda_slide(
            title_slide_html=title_slide_html,
            **common_params
        )
    else:
        return generator.generate_general_slide(
            existing_slide_content=existing_slide_content,
            **common_params
        )


@app.post("/export-powerpoint")
async def export_powerpoint_endpoint(req: HTMLInput):
    try:
        print(f"✅ API call received for PowerPoint export. {len(req.html_slides)} slides to export")

        # Use the translator module to handle the complete PowerPoint generation pipeline
        result = await generate_powerpoint_from_html(req.html_slides, llm_translate)

        return PresentationResponse(
            html_slides=req.html_slides,
            presentation_js=result['presentation_js'],
            download_url=result['download_url'],
            status="success",
            message=f"Generated PowerPoint file for {len(req.html_slides)} slides"
        )

    except Exception as e:
        print(f"❌ An error occurred in PowerPoint export: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/export-pdf")
async def export_pdf_endpoint(req: HTMLInput):
    """Convert HTML slides to PDF by rendering to PNG images first"""
    try:
        print(f"✅ API call received for PDF export. {len(req.html_slides)} slides to export")

        # Use the HTML renderer to convert slides to images, then combine into PDF
        from htmlrender.renderer import HTMLRenderer

        # Check if reportlab is available, if not provide helpful error
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.units import inch
        except ImportError:
            raise HTTPException(
                status_code=500,
                detail="PDF export requires 'reportlab' library. Please install it with: pip install reportlab"
            )

        # Create HTML renderer with 16:9 aspect ratio
        renderer = HTMLRenderer(size=(1280, 720))  # 16:9 aspect ratio

        # Generate timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_filename = f"presentation_{timestamp}.pdf"

        # Create temp directory
        temp_dir = "temp_presentations"
        os.makedirs(temp_dir, exist_ok=True)
        pdf_file_path = os.path.join(temp_dir, pdf_filename)

        # Create PDF with 16:9 landscape orientation (like PowerPoint)
        # Standard PowerPoint slide size: 10" x 7.5" (13.33" x 7.5" for 16:9)
        presentation_width = 13.33 * inch  # 16:9 aspect ratio width
        presentation_height = 7.5 * inch   # 16:9 aspect ratio height
        c = canvas.Canvas(pdf_file_path, pagesize=(presentation_width, presentation_height))
        page_width, page_height = presentation_width, presentation_height

        for i, slide_data in enumerate(req.html_slides):
            try:
                html_content = slide_data.get('html_content', '')
                slide_number = slide_data.get('slide_number', i+1)

                print(f"🔄 Rendering slide {slide_number} to image...")

                # Render HTML to image
                temp_image_name = f"slide_{slide_number}_{timestamp}.png"
                pil_image = renderer.renderHTML(html_content, temp_image_name)

                # Convert PIL image to fit PDF page
                img_width, img_height = pil_image.size

                # Calculate scaling to fit page while maintaining aspect ratio
                scale_w = (page_width - 40) / img_width  # 20pt margin on each side
                scale_h = (page_height - 40) / img_height  # 20pt margin on top/bottom
                scale = min(scale_w, scale_h)

                new_width = img_width * scale
                new_height = img_height * scale

                # Center the image on the page
                x = (page_width - new_width) / 2
                y = (page_height - new_height) / 2

                # Save PIL image to temporary file for ReportLab
                temp_img_path = os.path.join(temp_dir, f"temp_slide_{slide_number}_{timestamp}.png")
                pil_image.save(temp_img_path, format='PNG')

                # Add image to PDF
                c.drawImage(temp_img_path, x, y, width=new_width, height=new_height)

                # Clean up temporary image file
                try:
                    os.remove(temp_img_path)
                except:
                    pass  # Ignore cleanup errors

                # Add new page for next slide (except for last slide)
                if i < len(req.html_slides) - 1:
                    c.showPage()

                print(f"✅ Added slide {slide_number} to PDF")

            except Exception as slide_error:
                print(f"❌ Error processing slide {i+1}: {slide_error}")
                # Add error page
                c.setFont("Helvetica", 16)
                c.drawString(50, page_height - 100, f"Error rendering slide {i+1}")
                c.drawString(50, page_height - 130, str(slide_error))
                if i < len(req.html_slides) - 1:
                    c.showPage()

        # Save PDF
        c.save()
        print(f"✅ PDF generated: {pdf_file_path}")

        return PresentationResponse(
            html_slides=req.html_slides,
            presentation_js="",  # Not applicable for PDF
            download_url=f"/download-presentation/{pdf_filename}",
            status="success",
            message=f"Generated PDF with {len(req.html_slides)} slides"
        )

    except Exception as e:
        print(f"❌ An error occurred in PDF export: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/download-presentation/{filename}")
async def download_presentation(filename: str):
    try:
        file_path = os.path.join("temp_presentations", filename)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Presentation file not found")

        # Determine media type based on file extension
        if filename.endswith('.pptx'):
            media_type = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        elif filename.endswith('.pdf'):
            media_type = 'application/pdf'
        elif filename.endswith('.js'):
            media_type = 'application/javascript'
        else:
            media_type = 'application/octet-stream'

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type
        )

    except Exception as e:
        print(f"❌ An error occurred in file download: {e}")
        raise HTTPException(status_code=500, detail=str(e))