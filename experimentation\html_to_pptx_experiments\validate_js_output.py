"""
JavaScript Output Validation for PptxGenJS Code
"""
import re
import ast
from typing import Dict, Any


def validate_js_syntax(js_code: str) -> bool:
    """
    Basic JavaScript syntax validation
    """
    try:
        # Check for basic function structure
        if 'function createPresentation()' not in js_code:
            return False
        
        # Check for required PptxGenJS elements
        if 'new PptxGenJS()' not in js_code:
            return False
            
        if 'writeFile' not in js_code:
            return False
        
        # Check for balanced braces
        open_braces = js_code.count('{')
        close_braces = js_code.count('}')
        if open_braces != close_braces:
            return False
        
        # Check for balanced parentheses
        open_parens = js_code.count('(')
        close_parens = js_code.count(')')
        if open_parens != close_parens:
            return False
        
        # Check for unescaped single quotes (common error)
        # Look for patterns like 'text's' which should be 'text\'s'
        unescaped_quotes = re.findall(r"'[^']*'[^']*'", js_code)
        if unescaped_quotes:
            return False
        
        return True
        
    except Exception as e:
        print(f"Validation error: {e}")
        return False


def check_positioning_bounds(js_code: str) -> bool:
    """
    Check if positioning values are within safe bounds
    """
    try:
        # Extract x positions
        x_positions = re.findall(r'x:\s*([0-9]+\.?[0-9]*)', js_code)
        for x_str in x_positions:
            x = float(x_str)
            if x < 0.5 or x > 9.5:
                return False
        
        # Extract y positions
        y_positions = re.findall(r'y:\s*([0-9]+\.?[0-9]*)', js_code)
        for y_str in y_positions:
            y = float(y_str)
            if y < 0.5 or y > 5.0:
                return False
        
        return True
        
    except Exception as e:
        print(f"Positioning check error: {e}")
        return False


def check_color_format(js_code: str) -> bool:
    """
    Check if colors are properly formatted
    """
    try:
        # Look for color patterns
        color_patterns = re.findall(r"color:\s*['\"]([^'\"]*)['\"]", js_code)
        
        for color in color_patterns:
            # Check if it's a valid hex color (3 or 6 characters)
            if not re.match(r'^[A-Fa-f0-9]{3}$|^[A-Fa-f0-9]{6}$', color):
                # Allow some common color names
                if color.lower() not in ['red', 'blue', 'green', 'black', 'white', 'gray']:
                    return False
        
        return True
        
    except Exception as e:
        print(f"Color format check error: {e}")
        return False


def main(js_code: str) -> Dict[str, Any]:
    """
    Main validation function called by Prompt Flow
    """
    results = {
        'syntax_valid': validate_js_syntax(js_code),
        'positioning_valid': check_positioning_bounds(js_code),
        'colors_valid': check_color_format(js_code),
        'validation_errors': []
    }
    
    # Collect specific errors
    if not results['syntax_valid']:
        results['validation_errors'].append('Invalid JavaScript syntax')
    
    if not results['positioning_valid']:
        results['validation_errors'].append('Positioning values out of bounds')
    
    if not results['colors_valid']:
        results['validation_errors'].append('Invalid color format')
    
    # Overall validation
    results['overall_valid'] = all([
        results['syntax_valid'],
        results['positioning_valid'],
        results['colors_valid']
    ])
    
    return results


if __name__ == "__main__":
    # For testing
    test_code = """
    function createPresentation() {
        const pptx = new PptxGenJS();
        const slide = pptx.addSlide();
        slide.background = { color: 'FFFFFF' };
        slide.addText('Test Title', {
            x: 1.0, y: 0.8, w: 8.0, h: 1.0,
            fontSize: 32, color: '2C3E50', bold: true
        });
        return pptx.writeFile({ fileName: 'presentation.pptx' });
    }
    """
    
    result = main(test_code)
    print(f"Validation result: {result}")
