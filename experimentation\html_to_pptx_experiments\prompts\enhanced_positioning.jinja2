You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code with E<PERSON><PERSON><PERSON>ED POSITIONING ACCURACY.

CRITICAL POSITIONING RULES - FOLLOW EXACTLY:

**SLIDE DIMENSIONS & SAFE ZONES:**
- Standard slide: 10 inches wide × 5.625 inches tall
- SAFE MARGINS: Keep ALL content between x: 0.5-9.5 and y: 0.5-5.0
- NEVER exceed these boundaries or content will be cut off

**VERTICAL POSITIONING HIERARCHY:**
- TITLE_Y = 0.8 (always for main titles - h1, .title)
- SUBTITLE_Y = 1.4 (for subtitles - h2, .subtitle)  
- CONTENT_START_Y = 2.0 (first content element after title/subtitle)
- Y_INCREMENT = 0.7 (minimum spacing between content elements)
- MAX_Y = 4.5 (never exceed this boundary)

**HORIZONTAL POSITIONING:**
- Single column: x: 1.0, w: 8.0 (centered with margins)
- Two columns: x: 0.5, w: 4.0 and x: 5.25, w: 4.0
- Three columns: x: 0.5, w: 2.8, x: 3.5, w: 2.8, x: 6.5, w: 2.8

**ELEMENT SIZING:**
- Title font: 32-40px → fontSize: 36
- Subtitle font: 20-28px → fontSize: 24  
- Content font: 14-18px → fontSize: 16
- Small text: 12px → fontSize: 12

STEP 1: ANALYZE HTML STRUCTURE
Before generating code, identify:
- Main title (h1, .title) → position at y: 0.8
- Subtitle (h2, .subtitle) → position at y: 1.4
- Content sections → start at y: 2.0, increment by 0.7
- Grid layouts → use column positioning rules

STEP 2: EXTRACT COLORS DYNAMICALLY
- Background: Look for background-color, background, theme colors
- Text colors: Extract from color properties, CSS classes
- Format: Remove # symbol, ensure 6-digit hex: #2C3E50 → '2C3E50'

STEP 3: GENERATE PPTXGENJS CODE
```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // Background (extracted from HTML)
    slide.background = { color: 'EXTRACTED_COLOR' };
    
    // Title (always at y: 0.8)
    slide.addText('TITLE_TEXT', {
        x: 1.0, y: 0.8, w: 8.0, h: 0.8,
        fontSize: 36, color: 'EXTRACTED_COLOR', bold: true,
        align: 'left'
    });
    
    // Subtitle (if exists, at y: 1.4)
    slide.addText('SUBTITLE_TEXT', {
        x: 1.0, y: 1.4, w: 8.0, h: 0.6,
        fontSize: 24, color: 'EXTRACTED_COLOR',
        align: 'left'
    });
    
    // Content (start at y: 2.0, increment by 0.7)
    slide.addText('CONTENT_TEXT', {
        x: 1.0, y: 2.0, w: 8.0, h: 0.6,
        fontSize: 16, color: 'EXTRACTED_COLOR'
    });
    
    return pptx.writeFile({ fileName: 'presentation.pptx' });
}
```

VALIDATION CHECKLIST:
✅ All y positions between 0.5 and 4.5
✅ All x positions between 0.5 and 9.5  
✅ Title at y: 0.8, subtitle at y: 1.4
✅ Content starts at y: 2.0 minimum
✅ Minimum 0.7 spacing between elements
✅ Colors are 6-digit hex without # symbol
✅ All text properly quoted and escaped

HTML to convert:
{{html_content}}

Slide name: {{slide_name}}

Generate ONLY the JavaScript function with PERFECT positioning.
