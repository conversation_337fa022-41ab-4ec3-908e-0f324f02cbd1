#!/usr/bin/env python3
"""
Test script for Microsoft Prompt Flow HTML-to-PptxGenJS experimentation
"""

import sys
import os
import json
from pathlib import Path

# Load environment variables FIRST (before importing LLM modules)
from dotenv import load_dotenv

# Try multiple locations for environment file
env_paths = ["../local.env", "../.env", "local.env", ".env"]
env_loaded = False

for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"🔑 Environment variables loaded from: {env_path}")
        env_loaded = True
        break

if not env_loaded:
    print("⚠️  No environment file found. Please set API keys manually.")

# Add parent directory to path
sys.path.append("../")

# Import modules (after loading env vars)
from llm.llmwrapper import LLM
from pptx_generation.html_to_pptx_translator import llm_html_to_pptxgenjs_single

# Import validation functions
sys.path.append("html_to_pptx_experiments")
from validate_js_output import main as validate_js
from evaluate_translation import main as evaluate_translation

async def test_prompt_flow_setup():
    """Test basic Prompt Flow setup"""
    print("🧪 Testing Microsoft Prompt Flow Setup")
    print("=" * 50)
    
    # Initialize LLM
    print("1. Initializing LLM...")
    llm = LLM(provider="gemini", model="gemini-1.5-flash")
    print(f"   ✅ LLM initialized: {llm.provider} {llm.model}")
    
    # Test HTML sample
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Slide</title>
        <style>
            body { background-color: #2C3E50; color: #FFFFFF; font-family: Arial; }
            .title { font-size: 2em; color: #FFFFFF; font-weight: bold; }
            .subtitle { font-size: 1.2em; color: #A0C4FF; }
        </style>
    </head>
    <body>
        <h1 class="title">Test Title Slide</h1>
        <p class="subtitle">This is a test subtitle for experimentation</p>
        <p>This is test content to verify the translation works correctly.</p>
    </body>
    </html>
    """
    
    print("\n2. Testing HTML-to-PptxGenJS translation...")
    print(f"   - HTML length: {len(test_html)} characters")
    
    # Test current translator
    try:
        js_code = await llm_html_to_pptxgenjs_single(test_html, "test_slide", llm)
        print(f"   ✅ Translation successful: {len(js_code)} characters")
        print(f"   - Preview: {js_code[:150]}...")
    except Exception as e:
        print(f"   ❌ Translation failed: {e}")
        return False
    
    print("\n3. Testing validation functions...")
    
    # Test validation
    try:
        validation_result = validate_js(js_code)
        print(f"   ✅ Validation complete:")
        print(f"      - Syntax valid: {validation_result.get('syntax_valid', False)}")
        print(f"      - Positioning valid: {validation_result.get('positioning_valid', False)}")
        print(f"      - Colors valid: {validation_result.get('colors_valid', False)}")
        print(f"      - Overall valid: {validation_result.get('overall_valid', False)}")
    except Exception as e:
        print(f"   ❌ Validation failed: {e}")
        return False
    
    # Test quality evaluation
    try:
        quality_result = evaluate_translation(js_code, test_html)
        print(f"   ✅ Quality evaluation complete:")
        print(f"      - Color extraction: {quality_result.get('color_extraction_score', 0)}/100")
        print(f"      - Positioning: {quality_result.get('positioning_score', 0)}/100")
        print(f"      - Content preservation: {quality_result.get('content_preservation_score', 0)}/100")
        print(f"      - Overall quality: {quality_result.get('overall_quality_score', 0)}/100")
    except Exception as e:
        print(f"   ❌ Quality evaluation failed: {e}")
        return False
    
    print("\n4. Testing prompt variants...")
    
    # Test if prompt files exist
    prompt_dir = Path("html_to_pptx_experiments/prompts")
    prompt_files = ["baseline.jinja2", "enhanced_positioning.jinja2", "improved_colors.jinja2", "comprehensive_v2.jinja2"]
    
    for prompt_file in prompt_files:
        prompt_path = prompt_dir / prompt_file
        if prompt_path.exists():
            print(f"   ✅ {prompt_file} exists ({prompt_path.stat().st_size} bytes)")
        else:
            print(f"   ❌ {prompt_file} missing")
    
    print("\n🎉 Prompt Flow setup test complete!")
    print("=" * 50)
    print("✅ Ready for experimentation!")
    print("\nNext steps:")
    print("1. Open experimentation/prompt_flow_experimentation.ipynb")
    print("2. Run the cells to test different prompt variants")
    print("3. Compare results and identify best-performing prompts")
    
    return True

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_prompt_flow_setup())
