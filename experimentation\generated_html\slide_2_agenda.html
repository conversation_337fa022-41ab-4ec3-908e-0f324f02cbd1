<!--
Generated by: HTML-to-PptxGenJS Experimentation Notebook
Timestamp: 2025-08-10 15:40:24
Query: Make me a presentation about building a system which takes an internal company database of contract ...
Slide: slide_2 (agenda)
Input Tokens: 1053
Output Tokens: 819
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agenda</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
    


/* AUTO-FIT CSS TEMPLATE - Ensures content fits within 720px height */
body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
    position: relative;
    background-color: #FFFFFF;
    font-family: 'Roboto', sans-serif;
    overflow: hidden;
    color: #272774;
    display: flex;
    flex-direction: column;
}

/* Main content container with auto-fit */
.slide-content {
    position: absolute;
    top: 60px;
    left: 60px;
    right: 60px;
    bottom: 80px; /* Leave space for footer */
    max-width: 1160px;
    max-height: 580px; /* 720 - 60 (top) - 80 (bottom) */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1;
}

/* Auto-scaling title */
.slide-title {
    font-size: clamp(1.8em, 4vw, 2.3em); /* Responsive title size */
    font-weight: 700;
    margin-bottom: clamp(15px, 3vh, 30px);
    color: #272774;
    flex-shrink: 0; /* Don't shrink title */
    line-height: 1.2;
}

/* Content area that auto-fits remaining space */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* Allow flex shrinking */
}

/* Split content layout */
.split-content {
    display: flex;
    gap: clamp(20px, 3vw, 40px);
    flex: 1;
    min-height: 0;
}

/* Sections that auto-scale */
.visual-section, .bullet-points-section {
    flex: 1;
    background: #F5E2DA;
    border-radius: 8px;
    padding: clamp(10px, 2vh, 20px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

/* Auto-scaling text elements - Improved readability */
.content-text {
    font-size: clamp(1.0em, 1.5vw, 1.2em);  /* Increased min from 0.8em to 1.0em */
    line-height: 1.4;
    margin-bottom: clamp(8px, 1.5vh, 15px);
}

/* Bullet points with auto-scaling - Better readability */
ul {
    margin: 0;
    padding-left: 20px;
    flex: 1;
    overflow: hidden;
}

li {
    font-size: clamp(0.9em, 1.4vw, 1.1em);  /* Increased min from 0.75em to 0.9em */
    line-height: 1.5;
    margin-bottom: clamp(5px, 1vh, 10px);
    display: flex;
    align-items: flex-start;
}

/* Icon sizing */
.icon {
    margin-right: 10px;
    flex-shrink: 0;
    font-size: clamp(0.8em, 1.2vw, 1em);
    padding-top: 2px;
}

/* Image placeholders that scale */
.image-placeholder {
    position: relative;
    width: 100%;
    height: clamp(120px, 25vh, 200px);
    background: #FFFFFF;
    border: 2px solid #272774;
    border-radius: 8px;
    margin-bottom: clamp(10px, 2vh, 20px);
    flex-shrink: 0;
}

/* Progress bars and other elements */
.progress-bar-container {
    margin-top: auto;
    padding-top: clamp(5px, 1vh, 10px);
}

/* Fixed elements (logo, page number) */
.page-number {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #EBC4B4;
    color: #272774;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    font-size: clamp(12px, 1.2vw, 14px);
    z-index: 10;
}

.logo {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 10;
}

.logo img {
    height: clamp(40px, 6vh, 60px);
}

/* Responsive adjustments for content-heavy slides */
@media (max-height: 720px) {
    .slide-title {
        font-size: 1.8em;
        margin-bottom: 15px;
    }
    
    .content-text, li {
        font-size: 0.9em;  /* Increased from 0.8em */
        line-height: 1.3;
        margin-bottom: 8px;
    }
    
    .visual-section, .bullet-points-section {
        padding: 15px;
    }
    
    .image-placeholder {
        height: 120px;
        margin-bottom: 10px;
    }
}

/* Utility classes for different content densities - Improved readability */
.content-light .slide-title { font-size: 2.5em; }
.content-light .content-text { font-size: 1.1em; }
.content-light li { font-size: 1em; }

.content-medium .slide-title { font-size: 2.2em; }
.content-medium .content-text { font-size: 1.0em; }  /* Increased from 0.95em */
.content-medium li { font-size: 0.9em; }  /* Increased from 0.85em */

.content-heavy .slide-title { font-size: 2.1em; margin-bottom: 15px; }  /* Increased from 1.9em */
.content-heavy .content-text { font-size: 0.9em; line-height: 1.3; }  /* Increased from 0.8em */
.content-heavy li { font-size: 0.85em; line-height: 1.4; margin-bottom: 6px; }  /* Increased from 0.75em */
.content-heavy .visual-section, .content-heavy .bullet-points-section { padding: 12px; }
.content-heavy .image-placeholder { height: 100px; margin-bottom: 8px; }
</style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen" class="content-light">
    <div class="w-[1280px] h-[720px] bg-white shadow-xl flex flex-col p-16 relative overflow-hidden">
        <!-- Header Section -->
        <div class="flex items-center mb-12">
            <img src="https://img.freepik.com/premium-vector/abstract-ai-tech-logo-vector_568527-140.jpg" alt="AI Logo" class="h-16 w-16 mr-6 object-contain">
            <h1 class="text-6xl font-bold text-gray-800">Agenda</h1>
        </div>

        <!-- Agenda Items Section -->
        <div class="flex flex-col space-y-8 text-gray-700">
            <div class="flex items-start">
                <span class="text-4xl font-semibold text-blue-600 mr-6">01</span>
                <div>
                    <h3 class="text-4xl font-semibold mb-2 text-gray-800">The Challenge: Navigating Contract Data</h3>
                    <p class="text-2xl text-gray-600 ml-2">Understanding the current pain points in contract retrieval.</p>
                </div>
            </div>

            <div class="flex items-start">
                <span class="text-4xl font-semibold text-blue-600 mr-6">02</span>
                <div>
                    <h3 class="text-4xl font-semibold mb-2 text-gray-800">System Overview: Our Solution</h3>
                    <p class="text-2xl text-gray-600 ml-2">A high-level look at how our system addresses these challenges.</p>
                </div>
            </div>

            <div class="flex items-start">
                <span class="text-4xl font-semibold text-blue-600 mr-6">03</span>
                <div>
                    <h3 class="text-4xl font-semibold mb-2 text-gray-800">Key Components: Building Blocks for Success</h3>
                    <p class="text-2xl text-gray-600 ml-2">Deep dive into the essential technologies and modules.</p>
                </div>
            </div>

            <div class="flex items-start">
                <span class="text-4xl font-semibold text-blue-600 mr-6">04</span>
                <div>
                    <h3 class="text-4xl font-semibold mb-2 text-gray-800">Benefits & Future Vision</h3>
                    <p class="text-2xl text-gray-600 ml-2">The tangible advantages and potential for growth.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>