$schema: https://azuremlschemas.azureedge.net/promptflow/latest/Flow.schema.json
inputs:
  html_content:
    type: string
  slide_name:
    type: string
  prompt_variant:
    type: string
    default: "baseline"
outputs:
  pptxgenjs_code:
    type: string
    reference: ${translate_html.output}
  syntax_valid:
    type: bool
    reference: ${validate_output.syntax_valid}
  positioning_score:
    type: int
    reference: ${evaluate_quality.positioning_score}
  color_extraction_score:
    type: int
    reference: ${evaluate_quality.color_extraction_score}
nodes:
- name: translate_html
  type: prompt
  source:
    type: code
    path: prompts/${inputs.prompt_variant}.jinja2
  inputs:
    html_content: ${inputs.html_content}
    slide_name: ${inputs.slide_name}
- name: validate_output
  type: python
  source:
    type: code
    path: validate_js_output.py
  inputs:
    js_code: ${translate_html.output}
- name: evaluate_quality
  type: python
  source:
    type: code
    path: evaluate_translation.py
  inputs:
    js_code: ${translate_html.output}
    html_input: ${inputs.html_content}
environment:
  python_requirements_txt: requirements.txt
