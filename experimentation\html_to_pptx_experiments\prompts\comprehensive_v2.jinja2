You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code with COMPREHENSIVE ANALYSIS and ERROR PREVENTION.

🎯 **MISSION: PERFECT HTML-TO-PPTXGENJS TRANSLATION**

## PHASE 1: DEEP HTML ANALYSIS

**1.1 STRUCTURE ANALYSIS**
- Identify layout type: single-column, multi-column, grid, flex
- Map HTML hierarchy: title → subtitle → content sections
- Detect special elements: lists, icons, images, cards

**1.2 POSITIONING ANALYSIS**  
- Calculate relative positions from CSS
- Identify content flow and spacing
- Map to PowerPoint coordinate system

**1.3 STYLING ANALYSIS**
- Extract all colors (bg, text, borders, accents)
- Analyze typography (fonts, sizes, weights)
- Identify visual hierarchy and emphasis

## PHASE 2: POWERPOINT COORDINATE MAPPING

**2.1 SAFE POSITIONING SYSTEM**
```
SLIDE DIMENSIONS: 10" × 5.625"
SAFE ZONE: x: 0.5-9.5, y: 0.5-5.0

POSITIONING HIERARCHY:
├── TITLE_ZONE: y: 0.5-1.2 (main titles)
├── SUBTITLE_ZONE: y: 1.2-1.8 (subtitles)  
├── CONTENT_ZONE: y: 1.8-4.5 (main content)
└── FOOTER_ZONE: y: 4.5-5.0 (optional footer)

SPACING RULES:
- Minimum element spacing: 0.3"
- Recommended spacing: 0.5-0.7"
- Title-to-content gap: 0.8"
```

**2.2 LAYOUT PATTERNS**
```
Single Column: x: 1.0, w: 8.0
Two Columns: x: 0.5, w: 4.0 | x: 5.25, w: 4.0  
Three Columns: x: 0.5, w: 2.8 | x: 3.5, w: 2.8 | x: 6.5, w: 2.8
Grid 2x2: [0.5,1.8] [5.25,1.8] [0.5,3.2] [5.25,3.2]
```

## PHASE 3: ADVANCED COLOR EXTRACTION

**3.1 COLOR DETECTION CASCADE**
```javascript
// Priority order for color extraction:
1. style="color: #FF0000" (inline styles)
2. .text-blue-500 (Tailwind classes)  
3. .custom-color { color: #333; } (CSS classes)
4. :root { --primary: #2C3E50; } (CSS variables)
5. Professional defaults by theme
```

**3.2 COLOR PROCESSING PIPELINE**
```
Input: "background-color: #2C3E50"
Step 1: Extract hex → "#2C3E50"
Step 2: Remove # → "2C3E50"  
Step 3: Validate format → /^[A-Fa-f0-9]{6}$/
Step 4: Normalize case → "2C3E50"
Output: '2C3E50'
```

## PHASE 4: BULLETPROOF CODE GENERATION

**4.1 SYNTAX ERROR PREVENTION**
```javascript
// FORBIDDEN PATTERNS:
❌ 'text's content' (unescaped apostrophes)
❌ color: VARIABLE (unquoted variables)
❌ 'FF0000\' (trailing backslashes)
❌ y: 6.0 (out of bounds)

// CORRECT PATTERNS:
✅ 'text\'s content' (escaped apostrophes)
✅ color: 'FF0000' (quoted colors)
✅ 'FF0000' (clean strings)
✅ y: 4.0 (within bounds)
```

**4.2 POSITIONING VALIDATION**
```javascript
function validatePosition(x, y, w, h) {
    // Ensure within safe bounds
    x = Math.max(0.5, Math.min(x, 9.5 - w));
    y = Math.max(0.5, Math.min(y, 5.0 - h));
    w = Math.min(w, 9.0);
    h = Math.min(h, 4.5);
    return { x, y, w, h };
}
```

## PHASE 5: INTELLIGENT CONTENT MAPPING

**5.1 ELEMENT TYPE DETECTION**
```
HTML Element → PptxGenJS Mapping:
<h1>, .title → Main title (y: 0.8, fontSize: 36)
<h2>, .subtitle → Subtitle (y: 1.4, fontSize: 24)
<p>, .content → Body text (y: 2.0+, fontSize: 16)
<ul>, <ol> → Bullet lists (bullet: true)
.grid, .flex → Multi-column layout
.card, .box → Grouped content blocks
```

**5.2 CONTENT FLOW ALGORITHM**
```javascript
let currentY = 0.8; // Start after title
const elements = parseHTMLElements(html);

elements.forEach(element => {
    if (element.type === 'title') {
        currentY = 0.8; // Fixed title position
    } else if (element.type === 'subtitle') {
        currentY = 1.4; // Fixed subtitle position  
    } else {
        currentY = Math.max(currentY + 0.7, 2.0); // Content flow
    }
    
    // Ensure within bounds
    currentY = Math.min(currentY, 4.5);
});
```

## PHASE 6: TEMPLATE GENERATION

```javascript
function createPresentation() {
    const pptx = new PptxGenJS();
    const slide = pptx.addSlide();
    
    // EXTRACTED BACKGROUND
    slide.background = { color: 'ANALYZED_BG_COLOR' };
    
    // POSITIONED TITLE
    slide.addText('EXTRACTED_TITLE', {
        x: 1.0, y: 0.8, w: 8.0, h: 0.8,
        fontSize: 36, color: 'ANALYZED_TITLE_COLOR', 
        bold: true, align: 'left'
    });
    
    // POSITIONED SUBTITLE (if exists)
    slide.addText('EXTRACTED_SUBTITLE', {
        x: 1.0, y: 1.4, w: 8.0, h: 0.6,
        fontSize: 24, color: 'ANALYZED_SUBTITLE_COLOR',
        align: 'left'
    });
    
    // POSITIONED CONTENT
    slide.addText('EXTRACTED_CONTENT', {
        x: 1.0, y: 2.0, w: 8.0, h: 0.6,
        fontSize: 16, color: 'ANALYZED_CONTENT_COLOR'
    });
    
    return pptx.writeFile({ fileName: 'presentation.pptx' });
}
```

**FINAL VALIDATION CHECKLIST:**
✅ All positions within safe bounds (x: 0.5-9.5, y: 0.5-5.0)
✅ Proper element spacing (minimum 0.3" between elements)
✅ Colors properly formatted (6-digit hex, quoted, no #)
✅ Text properly escaped (apostrophes, quotes)
✅ Logical content hierarchy (title → subtitle → content)
✅ Appropriate font sizes (title: 36, subtitle: 24, content: 16)

HTML to convert:
{{html_content}}

Slide name: {{slide_name}}

Generate PERFECT, ERROR-FREE PptxGenJS code following ALL rules above.
