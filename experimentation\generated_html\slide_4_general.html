<!--
Generated by: HTML-to-PptxGenJS Experimentation Notebook
Timestamp: 2025-08-10 15:40:24
Query: Make me a presentation about building a system which takes an internal company database of contract ...
Slide: slide_4 (general)
Input Tokens: 3540
Output Tokens: 2359
-->
<!DOCTYPE html><html><head><meta charset="utf-8"></head><body><div class="w-full h-[720px] bg-white p-10 flex flex-col font-sans">
    <h2 class="text-4xl font-bold text-gray-800 mb-8 text-center">System Overview: Our Solution</h2>

    <div class="flex flex-grow gap-8">
        <!-- Left Section: Bullet Points -->
        <div class="w-1/2 flex flex-col justify-around text-gray-700">
            <div class="mb-6">
                <h3 class="text-2xl font-semibold text-blue-700 mb-3">User Query to Relevant Document</h3>
                <ul class="list-disc pl-6 space-y-2 text-lg">
                    <li><strong>Intuitive Interface</strong>: Users submit natural language queries.</li>
                    <li><strong>Intelligent Matching</strong>: System processes query, identifies pertinent documents.</li>
                    <li><strong>Ranked Results</strong>: Presents documents ordered by relevance with snippets.</li>
                </ul>
            </div>

            <div class="mb-6">
                <h3 class="text-2xl font-semibold text-blue-700 mb-3">Leveraging Internal Database</h3>
                <ul class="list-disc pl-6 space-y-2 text-lg">
                    <li><strong>Secure Integration</strong>: Connects directly to existing contract database.</li>
                    <li><strong>Comprehensive Indexing</strong>: All documents processed for rapid search.</li>
                    <li><strong>Data Integrity</strong>: Maintains original structure and content.</li>
                </ul>
            </div>

            <div>
                <h3 class="text-2xl font-semibold text-blue-700 mb-3">Enhancing Decision-Making</h3>
                <ul class="list-disc pl-6 space-y-2 text-lg">
                    <li><strong>Rapid Information Access</strong>: Empowers teams with immediate insights.</li>
                    <li><strong>Reduced Risk</strong>: Minimizes errors with accurate, up-to-date information.</li>
                    <li><strong>Strategic Advantage</strong>: Frees up time for analysis and planning.</li>
                </ul>
            </div>
        </div>

        <!-- Right Section: System Flow Diagram -->
        <div class="w-1/2 flex flex-col items-center justify-center relative">
            <h3 class="text-2xl font-semibold text-gray-800 mb-6">Relevant Contract Retrieval System Flow</h3>

            <div class="relative w-full h-[450px] flex items-center justify-center">
                <!-- Components -->
                <div class="absolute top-[10%] left-[5%] flex flex-col items-center">
                    <img src="https://static.vecteezy.com/system/resources/previews/017/800/528/original/user-simple-flat-icon-illustration-vector.jpg" alt="User Icon" class="w-16 h-16 mb-2">
                    <span class="text-sm font-medium">User</span>
                </div>

                <div class="absolute top-[10%] left-[25%] flex flex-col items-center p-3 bg-blue-100 rounded-lg shadow-md">
                    <span class="text-sm font-medium text-center">User Interface (UI)</span>
                </div>

                <div class="absolute top-[10%] right-[25%] flex flex-col items-center p-3 bg-blue-100 rounded-lg shadow-md">
                    <span class="text-sm font-medium text-center">Search & Ranking Engine</span>
                </div>

                <div class="absolute top-[10%] right-[5%] flex flex-col items-center p-3 bg-blue-100 rounded-lg shadow-md">
                    <span class="text-sm font-medium text-center">Search Index</span>
                </div>

                <div class="absolute top-[45%] left-[25%] flex flex-col items-center p-3 bg-blue-100 rounded-lg shadow-md">
                    <span class="text-sm font-medium text-center">Natural Language Processing (NLP) Module</span>
                </div>

                <div class="absolute bottom-[5%] left-[5%] flex flex-col items-center">
                    <img src="https://cdn2.vectorstock.com/i/1000x1000/19/76/database-icon-simple-minimal-96x96-pictograph-vector-19831976.jpg" alt="Database Icon" class="w-16 h-16 mb-2">
                    <span class="text-sm font-medium text-center">Internal Contract Database</span>
                </div>

                <div class="absolute bottom-[5%] right-[25%] flex flex-col items-center p-3 bg-blue-100 rounded-lg shadow-md">
                    <span class="text-sm font-medium text-center">Data Ingestion & Indexing Module</span>
                </div>

                <!-- Arrows/Flow -->
                <!-- User -> UI -->
                <div class="absolute top-[15%] left-[18%] w-[7%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute top-[15%] left-[24.5%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-l-8 border-l-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute top-[12%] left-[19%] text-xs text-gray-600">Query</span>

                <!-- UI -> NLP Module -->
                <div class="absolute top-[20%] left-[35%] h-[25%] w-0.5 bg-gray-500"></div>
                <div class="absolute top-[45%] left-[35%] w-0 h-0 border-l-4 border-l-transparent border-r-4 border-r-transparent border-t-8 border-t-gray-500"></div>
                <span class="absolute top-[30%] left-[36%] text-xs text-gray-600 rotate-90">Processed Query</span>

                <!-- NLP Module -> Search & Ranking Engine -->
                <div class="absolute top-[49%] left-[48%] w-[12%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute top-[49%] left-[60%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-l-8 border-l-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute top-[46%] left-[50%] text-xs text-gray-600">Semantic Features</span>

                <!-- Internal Contract Database -> Data Ingestion & Indexing Module -->
                <div class="absolute bottom-[10%] left-[18%] w-[7%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute bottom-[10%] left-[24.5%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-l-8 border-l-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute bottom-[13%] left-[19%] text-xs text-gray-600">Contract Documents</span>

                <!-- Data Ingestion & Indexing Module -> Search Index -->
                <div class="absolute bottom-[10%] right-[18%] w-[7%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute bottom-[10%] right-[17.5%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-l-8 border-l-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute bottom-[13%] right-[19%] text-xs text-gray-600">Indexed Data</span>

                <!-- Search Index -> Search & Ranking Engine -->
                <div class="absolute top-[20%] right-[10%] h-[25%] w-0.5 bg-gray-500"></div>
                <div class="absolute top-[45%] right-[10%] w-0 h-0 border-l-4 border-l-transparent border-r-4 border-r-transparent border-t-8 border-t-gray-500"></div>
                <span class="absolute top-[30%] right-[11%] text-xs text-gray-600 rotate-90">Indexed Data</span>

                <!-- Search & Ranking Engine -> UI -->
                <div class="absolute top-[15%] left-[60%] w-[7%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute top-[15%] left-[59.5%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-r-8 border-r-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute top-[12%] left-[50%] text-xs text-gray-600">Relevant Documents</span>

                <!-- UI -> User -->
                <div class="absolute top-[15%] left-[18%] w-[7%] h-0.5 bg-gray-500 transform -translate-y-1/2"></div>
                <div class="absolute top-[15%] left-[17.5%] w-0 h-0 border-t-4 border-t-transparent border-b-4 border-b-transparent border-r-8 border-r-gray-500 transform -translate-y-1/2"></div>
                <span class="absolute top-[12%] left-[19%] text-xs text-gray-600">Results</span>
            </div>
        </div>
    </div>
</div></body></html>