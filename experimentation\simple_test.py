#!/usr/bin/env python3
"""
Simple test for Prompt Flow validation functions
"""

import sys
import os
import json

# Test validation functions
sys.path.append("html_to_pptx_experiments")

def test_validation_functions():
    """Test validation and evaluation functions"""
    print("🧪 Testing Prompt Flow Validation Functions")
    print("=" * 50)
    
    # Test HTML sample
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Slide</title>
        <style>
            body { background-color: #2C3E50; color: #FFFFFF; }
            .title { font-size: 2em; color: #FFFFFF; }
        </style>
    </head>
    <body>
        <h1 class="title">Test Title</h1>
        <p>Test content</p>
    </body>
    </html>
    """
    
    # Test JavaScript sample
    test_js = """
    function createPresentation() {
        const pptx = new PptxGenJS();
        const slide = pptx.addSlide();
        slide.background = { color: '2C3E50' };
        slide.addText('Test Title', {
            x: 1.0, y: 0.8, w: 8.0, h: 1.0,
            fontSize: 32, color: 'FFFFFF', bold: true
        });
        slide.addText('Test content', {
            x: 1.0, y: 2.0, w: 8.0, h: 1.0,
            fontSize: 16, color: 'FFFFFF'
        });
        return pptx.writeFile({ fileName: 'presentation.pptx' });
    }
    """
    
    print("1. Testing validation functions...")
    
    try:
        from validate_js_output import main as validate_js
        validation_result = validate_js(test_js)
        print(f"   ✅ Validation successful:")
        print(f"      - Syntax valid: {validation_result.get('syntax_valid', False)}")
        print(f"      - Positioning valid: {validation_result.get('positioning_valid', False)}")
        print(f"      - Colors valid: {validation_result.get('colors_valid', False)}")
        print(f"      - Overall valid: {validation_result.get('overall_valid', False)}")
    except Exception as e:
        print(f"   ❌ Validation failed: {e}")
        return False
    
    print("\n2. Testing quality evaluation...")
    
    try:
        from evaluate_translation import main as evaluate_translation
        quality_result = evaluate_translation(test_js, test_html)
        print(f"   ✅ Quality evaluation successful:")
        print(f"      - Color extraction: {quality_result.get('color_extraction_score', 0)}/100")
        print(f"      - Positioning: {quality_result.get('positioning_score', 0)}/100")
        print(f"      - Content preservation: {quality_result.get('content_preservation_score', 0)}/100")
        print(f"      - Overall quality: {quality_result.get('overall_quality_score', 0)}/100")
    except Exception as e:
        print(f"   ❌ Quality evaluation failed: {e}")
        return False
    
    print("\n3. Testing prompt files...")
    
    # Check if prompt files exist
    import os
    prompt_dir = "html_to_pptx_experiments/prompts"
    prompt_files = ["baseline.jinja2", "enhanced_positioning.jinja2", "improved_colors.jinja2", "comprehensive_v2.jinja2"]
    
    for prompt_file in prompt_files:
        prompt_path = os.path.join(prompt_dir, prompt_file)
        if os.path.exists(prompt_path):
            size = os.path.getsize(prompt_path)
            print(f"   ✅ {prompt_file} exists ({size} bytes)")
        else:
            print(f"   ❌ {prompt_file} missing")
    
    print("\n🎉 Validation test complete!")
    print("=" * 50)
    print("✅ Prompt Flow validation system is working!")
    print("\nNext steps:")
    print("1. Set up API keys for LLM testing")
    print("2. Open experimentation/prompt_flow_experimentation.ipynb")
    print("3. Run experiments with different prompt variants")
    
    return True

if __name__ == "__main__":
    test_validation_functions()
