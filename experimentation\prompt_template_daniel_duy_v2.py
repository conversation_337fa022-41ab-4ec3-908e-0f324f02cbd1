import textwrap

### Reviewer prompt 
# Name changed from reviewerpy_slide_fix_prompt_daniel_v1
reviewerpy_slide_review_prompt_v2 = textwrap.dedent(
        """
        You are a senior front-end software engineer reviewing a junior engineer's work.
        He has written some HTML which is supposed to show one slide of a powerpoint.
        You have been provided with the HTML code and also a rendering of the code as an image.
        As you can see, slide components, visual elements or text is overflowing from the bottom of the slide.

        The HTML code is provided below:
        {code}

        You must fix this overflow issue, you could lose your job if you don't.
        Look at the HTML and the image, and follow the following chain of thought:
        1) Can I fix this problem by slightly reducing the font size, vertical padding/spacing between visual elements, titles, subtitles and content blocks?

        2) If 1 is not possible, I should remove content. Is there any slide content I can remove without significantly altering the main points of the slide?

        3) If 1 and 2 are not possible, I must completely restructure the slide such that all the content fits on the slide. How can I do this without significantly altering the main story of the slide?

        Once you have finished thinking, make the necessary changes to the HTML code and output ONLY the improved HTML code
        """
        )

### Title slide prompt generation.py
generationpy_title_slide_prompt_v2 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        You are trying to create a set of slides for a proposal.
        The first slide you want to create is the title slide.
        Generate the title slide in HTML.

        Take into consideration the following points:
        - Choose a style that is both visually appealing and functional; befitting of a proposal from a top-tier tech consulting company.
        - What colour and design would be appropriate, especially for the background?
        - What font type should you use?
        - What should the size of the page be, to accurately reflect powerpoint slides? The slides must be 720p
        - The title font should be around 3.0em, and the subtitle around 1.8em, otherwise it is too big.
        - Do not include a footer.
        - The HTML code MUST include the following configurations:
        .slide-container {{
                width: 1280px; /* 720p resolution width */
                height: 720px;
                ... The rest is not specified and to your discretion ...  
                }}

        .slide-content {{
                flex-grow: 1; /* Allows this section to take up available space */
                overflow : hidden; 
                display: flex;
                flex-direction: column;
                justify-content: flex-start; /* Align content to the top */
                align-items: flex-start; /* Align content to the left */
                text-align: left; /* Default text alignment */
                width: 100%; /* Ensure it takes full width of padding area */
                padding-top: 0; /* Remove extra padding from title slide */
                }}
        
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {title_slide_content}
        """
        )

### Agenda slide prompt generation.py
# Name changed from generationpy_agenda_slide_prompt_daniel_v3
generationpy_agenda_slide_prompt_v2 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal using HTML and TailwindCSS.
        You have finished the title slide.

        Title slide HTML:
        ```html
        {title_slide_html}
        ```

        Next, create the agenda slide in full HTML and Tailwind CSS.

        Slide content to include:
        {agenda_slide_content}
        
        You need to pay EXTREME ATTENTION to the position of the elements inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        Constraints:
        - Conform to the design style of the existing slides for consistency
        - Eliminate the use of animations and transitions.
        - The css component describing the main content area must be called .slide-content
        - DO NOT include the presenter name, their title, the date and any company logos on this slide. This is to save space.
        - Titles should be aligned to the top left-hand side of the slide
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.


        Beyond all your mission is to make this BEAUTIFUL
        REMEMBER: You must output FULL HTML and Tailwind CSS, starting with <!DOCTYPE html> and ending with </html> and NOTHING ELSE
        """
)

### General Slide Prompt generation.py
# Name changed from generationpy_slide_prompt_daniel_v3
generationpy_slide_prompt_v2 = textwrap.dedent(
            """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        You are trying to create a set of slides for a proposal using HTML and TailwindCSS, and have so far created some slides already: 

        {existing_slides}

        Create the slide in full HTML and Tailwind CSS.

        Slide content to include:
        {slide_content}
        
        You need to pay EXTREME ATTENTION to the position of the elements inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        Constraints:
        - Conform to the design style of the existing slides for consistency
        - Eliminate the use of animations and transitions.
        - The css component describing the main content area must be called .slide-content
        - Titles should be aligned to the top left-hand side of the slide
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.

        Beyond all your mission is to make this BEAUTIFUL
        REMEMBER: You must output FULL HTML and Tailwind CSS, starting with <!DOCTYPE html> and ending with </html> and NOTHING ELSE
        """ 
)

### Planning prompts
# The original daniel prompts
# Name changed from plannerpy_outline_prompt_daniel_v1
plannerpy_outline_prompt_v2 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:
        "{query}"

        Some of the sections you should include are:
        - Title slide
        - Executive summary slide
        - The background of the problem
        - Your proposed solution and why it will work / benefits of the solution
        - The infrastructure and tech stack
        - The required human resources
        - The timeline
        - The cost involved in this project
        - A proper conclusion slide
                
        Depending on the situation, be creative and add in any other sections that you think might add value.
        If this proposal is successful, you will get a big raise!
        """
        )

# Name changed from plannerpy_brainstorm_prompt_daniel_v1
plannerpy_brainstorm_prompt_v2 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        Before starting work on the request, you need to brainstorm.
        From a technical perspective, how could something like this be done? 
        Please use the following pointers to guide your thought process:
        - What is the most cutting-edge way to do this?
        - How can this be done using cloud services?
        """
        )

# Name changed from plannerpy_slide_content_prompt_daniel_v1
plannerpy_slide_content_prompt_v2 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        Based on the advice of the senior software engineer, you have planned out your presentation:
        "{outline_response}"

        CRITICAL INSTRUCTIONS:
        1) You MUST follow the outline structure EXACTLY - do not add extra slides beyond what's in the outline
        2) If the original request specified a number of slides (e.g., "1 slide", "3 slides"), you MUST respect that limit
        3) Create content ONLY for the slides mentioned in the outline above
        4) Do NOT create additional slides even if you think more content would be helpful

        Following the plan you have created above, and referencing the technical advice of the senior software engineer,
        describe the content that will appear on EACH slide in detail.

        Pay extra attention to the following points:
        1) If a diagram or image should go on a slide (e.g. an infrastructure diagram, organization chart or a GANTT chart etc.),
        you must describe it with enough detail such that someone reading the description would be able to reproduce it perfectly.

        2) Make sure to include the content that should appear on the title slide.

        3) Make sure to include at least one slide with a table. Output this table in markdown.

        4) STICK TO THE OUTLINE - Do not exceed the number of slides specified in the outline or original request.

        5) Remember that these are powerpoint slides, do not overload them with too much content

        Think carefully about whether or not the needs of the client are being met with this proposal.
        If this proposal is successful, you will get a big raise!

        IMPORTANT: Make sure to separate the content of each slide with the following markers <Slide X START> and <Slide X END>, where X represents the slide number.
        REMEMBER: Only create slides that are mentioned in the outline above. Do not add extra slides.
        """
    )
